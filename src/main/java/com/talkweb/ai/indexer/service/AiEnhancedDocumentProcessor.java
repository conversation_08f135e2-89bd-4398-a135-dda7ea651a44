package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.DocumentProcessor;
import com.talkweb.ai.indexer.core.ProcessingContext;
import com.talkweb.ai.indexer.core.ProcessingResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * AI-enhanced document processor that adds intelligent analysis to document conversion
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Service
@ConditionalOnProperty(name = "ai.enabled", havingValue = "true", matchIfMissing = false)
public class AiEnhancedDocumentProcessor {

    private static final Logger logger = LoggerFactory.getLogger(AiEnhancedDocumentProcessor.class);

    @Autowired(required = false)
    private DocumentSummaryService summaryService;

    @Autowired(required = false)
    private DocumentEmbeddingService embeddingService;

    /**
     * Processes a document with AI enhancement
     *
     * @param processor the base document processor
     * @param inputFile the input file
     * @param context the processing context
     * @return enhanced processing result with AI analysis
     */
    public ProcessingResult processWithAiEnhancement(DocumentProcessor processor, File inputFile, ProcessingContext context) {
        logger.info("Processing document with AI enhancement: {}", inputFile.getName());

        try {
            // First, perform the standard processing
            ProcessingResult result = processor.process(inputFile, context);

            if (!result.isSuccess()) {
                logger.warn("Base processing failed, skipping AI enhancement");
                return result;
            }

            // Convert ProcessingResult to ConversionResult for AI enhancement
            ConversionResult conversionResult = convertToConversionResult(result);

            // Add AI enhancements if services are available
            if (isAiEnabled() && conversionResult.getContent() != null && !conversionResult.getContent().trim().isEmpty()) {
                enhanceWithAiAnalysis(conversionResult, inputFile);
            }

            return result;

        } catch (Exception e) {
            logger.error("Failed to process document with AI enhancement: {}", inputFile.getName(), e);
            return ProcessingResult.failure("AI-enhanced processing failed: " + e.getMessage());
        }
    }

    /**
     * Converts ProcessingResult to ConversionResult for AI analysis
     */
    private ConversionResult convertToConversionResult(ProcessingResult processingResult) {
        if (processingResult.isSuccess() && processingResult.getOutputFile() != null) {
            try {
                // Read the output file content
                String content = java.nio.file.Files.readString(processingResult.getOutputFile().toPath());
                return new ConversionResult(ConversionResult.Status.SUCCESS, content, "Processing successful");
            } catch (Exception e) {
                logger.warn("Failed to read output file for AI analysis: {}", e.getMessage());
                return new ConversionResult(ConversionResult.Status.FAILED, "", "Failed to read output: " + e.getMessage());
            }
        }
        return new ConversionResult(ConversionResult.Status.FAILED, "", "Processing failed");
    }

    /**
     * Processes a document with AI enhancement asynchronously
     *
     * @param processor the base document processor
     * @param inputFile the input file
     * @param context the processing context
     * @return CompletableFuture with enhanced processing result
     */
    public CompletableFuture<ProcessingResult> processWithAiEnhancementAsync(DocumentProcessor processor, File inputFile, ProcessingContext context) {
        return CompletableFuture.supplyAsync(() -> processWithAiEnhancement(processor, inputFile, context));
    }

    /**
     * Enhances a conversion result with AI analysis
     * 
     * @param result the conversion result to enhance
     * @param inputFile the original input file
     */
    private void enhanceWithAiAnalysis(ConversionResult result, File inputFile) {
        String content = result.getContent();
        
        try {
            // Generate summary if summary service is available
            if (summaryService != null) {
                logger.debug("Generating AI summary for document: {}", inputFile.getName());
                String summary = summaryService.generateSummary(content, 200);
                result.setSummary(summary);
            }

            // Extract key points if summary service is available
            if (summaryService != null) {
                logger.debug("Extracting key points for document: {}", inputFile.getName());
                String keyPoints = summaryService.extractKeyPoints(content, 5);
                result.setKeyPoints(keyPoints);
            }

            // Generate content analysis if summary service is available
            if (summaryService != null) {
                logger.debug("Generating content analysis for document: {}", inputFile.getName());
                String analysis = summaryService.analyzeContent(content);
                result.setAnalysis(analysis);
            }

            // Generate embeddings if embedding service is available
            if (embeddingService != null) {
                logger.debug("Generating embeddings for document: {}", inputFile.getName());
                Map<String, Object> metadata = createMetadata(inputFile, result);
                embeddingService.generateEmbeddingsAsync(content, metadata)
                    .thenAccept(documents -> {
                        logger.info("Generated {} embedding chunks for document: {}", documents.size(), inputFile.getName());
                    })
                    .exceptionally(throwable -> {
                        logger.warn("Failed to generate embeddings for document: {}", inputFile.getName(), throwable);
                        return null;
                    });
            }

            logger.info("AI enhancement completed for document: {}", inputFile.getName());

        } catch (Exception e) {
            logger.error("Failed to enhance document with AI analysis: {}", inputFile.getName(), e);
            // Don't fail the entire conversion if AI enhancement fails
            result.setAnalysis("AI analysis failed: " + e.getMessage());
        }
    }

    /**
     * Creates metadata for the document
     * 
     * @param inputFile the input file
     * @param result the conversion result
     * @return metadata map
     */
    private Map<String, Object> createMetadata(File inputFile, ConversionResult result) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("filename", inputFile.getName());
        metadata.put("file_path", inputFile.getAbsolutePath());
        metadata.put("file_size", inputFile.length());
        metadata.put("content_length", result.getContent().length());
        metadata.put("conversion_status", result.getStatus().toString());
        metadata.put("processing_timestamp", System.currentTimeMillis());
        
        // Add file extension
        String fileName = inputFile.getName();
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            metadata.put("file_extension", fileName.substring(lastDot + 1).toLowerCase());
        }
        
        return metadata;
    }

    /**
     * Checks if AI services are enabled and available
     * 
     * @return true if AI enhancement is available
     */
    public boolean isAiEnabled() {
        return summaryService != null || embeddingService != null;
    }

    /**
     * Gets the status of AI services
     * 
     * @return map with AI service availability status
     */
    public Map<String, Boolean> getAiServiceStatus() {
        Map<String, Boolean> status = new HashMap<>();
        status.put("summary_service", summaryService != null);
        status.put("embedding_service", embeddingService != null);
        status.put("ai_enabled", isAiEnabled());
        return status;
    }
}
