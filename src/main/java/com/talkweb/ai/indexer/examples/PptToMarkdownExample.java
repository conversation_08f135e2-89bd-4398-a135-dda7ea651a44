package com.talkweb.ai.indexer.examples;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.util.PptToMarkdownConverter;
import com.talkweb.ai.indexer.util.ppt.PptConversionConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

/**
 * PowerPoint to Markdown converter usage examples
 * 
 * Demonstrates various ways to use the PPT/PPTX to Markdown converter
 * with different configuration options and scenarios.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class PptToMarkdownExample {
    
    private static final Logger logger = LoggerFactory.getLogger(PptToMarkdownExample.class);
    
    public static void main(String[] args) {
        try {
            System.out.println("=== PowerPoint to Markdown Converter Examples ===\n");
            
            // Example 1: Basic conversion
            basicConversionExample();
            
            // Example 2: High fidelity conversion
            highFidelityConversionExample();
            
            // Example 3: Performance optimized conversion
            performanceConversionExample();
            
            // Example 4: Plugin usage
            pluginUsageExample();
            
            // Example 5: Error handling
            errorHandlingExample();
            
            System.out.println("\n=== All examples completed successfully! ===");
            
        } catch (Exception e) {
            logger.error("Example execution failed", e);
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Example 1: Basic conversion with default settings
     */
    private static void basicConversionExample() {
        System.out.println("=== Example 1: Basic Conversion ===");
        
        try {
            // Create a test file (in real usage, this would be an existing PPT file)
            File testFile = createTestPptFile();
            
            // Simple conversion with default settings
            String markdown = PptToMarkdownConverter.convertToMarkdown(testFile);
            
            System.out.println("✓ Basic conversion completed");
            System.out.println("Output preview:");
            System.out.println(markdown.substring(0, Math.min(200, markdown.length())) + "...");
            
        } catch (Exception e) {
            System.out.println("✗ Basic conversion failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Example 2: High fidelity conversion with all features enabled
     */
    private static void highFidelityConversionExample() {
        System.out.println("=== Example 2: High Fidelity Conversion ===");
        
        try {
            File testFile = createTestPptFile();
            
            // Create high fidelity configuration
            PptConversionConfig config = PptConversionConfig.createHighFidelityConfig();
            
            System.out.println("Configuration:");
            System.out.println("  - Include metadata: " + config.isIncludeMetadata());
            System.out.println("  - Include speaker notes: " + config.isIncludeSpeakerNotes());
            System.out.println("  - Extract images: " + config.isExtractImages());
            System.out.println("  - Extract tables: " + config.isExtractTables());
            System.out.println("  - Extract charts: " + config.isExtractCharts());
            
            // Convert with high fidelity settings
            String markdown = PptToMarkdownConverter.convertToMarkdown(testFile, config);
            
            System.out.println("✓ High fidelity conversion completed");
            System.out.println("Output length: " + markdown.length() + " characters");
            
        } catch (Exception e) {
            System.out.println("✗ High fidelity conversion failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Example 3: Performance optimized conversion
     */
    private static void performanceConversionExample() {
        System.out.println("=== Example 3: Performance Optimized Conversion ===");
        
        try {
            File testFile = createTestPptFile();
            
            // Create performance optimized configuration
            PptConversionConfig config = PptConversionConfig.createPerformanceConfig();
            
            System.out.println("Performance configuration:");
            System.out.println("  - Include metadata: " + config.isIncludeMetadata());
            System.out.println("  - Extract images: " + config.isExtractImages());
            System.out.println("  - Enable caching: " + config.isEnableCaching());
            System.out.println("  - Stream processing: " + config.isStreamProcessing());
            
            long startTime = System.currentTimeMillis();
            String markdown = PptToMarkdownConverter.convertToMarkdown(testFile, config);
            long endTime = System.currentTimeMillis();
            
            System.out.println("✓ Performance conversion completed in " + (endTime - startTime) + "ms");
            System.out.println("Output length: " + markdown.length() + " characters");
            
        } catch (Exception e) {
            System.out.println("✗ Performance conversion failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Example 4: Using the converter as a plugin
     */
    private static void pluginUsageExample() throws Exception {
        System.out.println("=== Example 4: Plugin Usage ===");
        
        // Create plugin metadata
        PluginMetadata metadata = PluginMetadata.builder()
            .id("ppt-converter-example")
            .name("PPT Converter Example")
            .version("1.0.0")
            .description("Example usage of PPT to Markdown converter")
            .provider("Example Provider")
            .className("com.talkweb.ai.indexer.util.PptToMarkdownConverter")
            .build();
        
        // Create converter instance
        PptToMarkdownConverter converter = new PptToMarkdownConverter(metadata);
        
        try {
            // Start the plugin
            converter.start();
            System.out.println("✓ Plugin started: " + converter.getState());
            
            // Create test file
            File testFile = createTestPptFile();
            
            // Convert using plugin interface
            ConversionResult result = converter.convert(testFile);
            
            // Process result
            if (result.getStatus() == ConversionResult.Status.SUCCESS) {
                System.out.println("✓ Plugin conversion successful");
                System.out.println("Content length: " + result.getContent().length());
            } else {
                System.out.println("✗ Plugin conversion failed: " + result.getMessage());
            }
            
        } finally {
            // Stop the plugin
            converter.stop();
            System.out.println("✓ Plugin stopped: " + converter.getState());
        }
        
        System.out.println();
    }
    
    /**
     * Example 5: Error handling scenarios
     */
    private static void errorHandlingExample() {
        System.out.println("=== Example 5: Error Handling ===");
        
        // Test 1: Non-existent file
        try {
            File nonExistentFile = new File("non_existent_file.pptx");
            PptToMarkdownConverter.convertToMarkdown(nonExistentFile);
            System.out.println("✗ Should have thrown exception for non-existent file");
        } catch (IllegalArgumentException e) {
            System.out.println("✓ Correctly handled non-existent file: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("✗ Unexpected exception: " + e.getMessage());
        }
        
        // Test 2: Invalid file format
        try {
            File invalidFile = new File("test.txt");
            PptToMarkdownConverter.convertToMarkdown(invalidFile);
            System.out.println("✗ Should have thrown exception for invalid file format");
        } catch (IllegalArgumentException e) {
            System.out.println("✓ Correctly handled invalid file format: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("✗ Unexpected exception: " + e.getMessage());
        }
        
        // Test 3: Strict vs Loose mode
        try {
            File testFile = createTestPptFile();
            
            // Strict mode
            PptConversionConfig strictConfig = PptConversionConfig.createStrictConfig();
            try {
                PptToMarkdownConverter.convertToMarkdown(testFile, strictConfig);
                System.out.println("✓ Strict mode conversion completed (or failed as expected)");
            } catch (RuntimeException e) {
                System.out.println("✓ Strict mode correctly threw exception: " + e.getMessage());
            }
            
            // Loose mode
            PptConversionConfig looseConfig = new PptConversionConfig().setStrictMode(false);
            String result = PptToMarkdownConverter.convertToMarkdown(testFile, looseConfig);
            System.out.println("✓ Loose mode handled gracefully, output length: " + result.length());
            
        } catch (Exception e) {
            System.out.println("✗ Error handling test failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * Creates a test PPT file for demonstration
     * Note: This creates a minimal file that will likely fail conversion,
     * but demonstrates the API usage
     */
    private static File createTestPptFile() {
        try {
            File tempFile = File.createTempFile("test_presentation", ".pptx");
            tempFile.deleteOnExit();
            
            // Write minimal PPTX signature (this won't be a valid PPTX file)
            java.nio.file.Files.write(tempFile.toPath(), 
                new byte[]{0x50, 0x4B, 0x03, 0x04, 0x00, 0x00, 0x00, 0x00});
            
            return tempFile;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test file", e);
        }
    }
    
    /**
     * Demonstrates custom configuration
     */
    private static void customConfigurationExample() {
        System.out.println("=== Custom Configuration Example ===");
        
        // Create custom configuration
        PptConversionConfig config = new PptConversionConfig()
            .setIncludeMetadata(true)
            .setIncludeSpeakerNotes(false)
            .setExtractImages(true)
            .setExtractTables(true)
            .setImageFormat("jpg")
            .setMaxImageWidth(1024)
            .setMaxImageHeight(768)
            .setImageOutputDirectory("custom_images")
            .setNormalizeWhitespace(true)
            .setConvertBulletsToMarkdown(true)
            .setEnableCaching(true)
            .setMaxCacheSize(50)
            .setProcessingTimeout(600);
        
        System.out.println("Custom configuration created:");
        System.out.println("  - Image format: " + config.getImageFormat());
        System.out.println("  - Max image size: " + config.getMaxImageWidth() + "x" + config.getMaxImageHeight());
        System.out.println("  - Image directory: " + config.getImageOutputDirectory());
        System.out.println("  - Cache size: " + config.getMaxCacheSize());
        System.out.println("  - Timeout: " + config.getProcessingTimeout() + "s");
        
        System.out.println("✓ Custom configuration ready for use");
        System.out.println();
    }
    
    /**
     * Demonstrates batch processing
     */
    private static void batchProcessingExample() {
        System.out.println("=== Batch Processing Example ===");
        
        try {
            // Create converter instance for batch processing
            PptToMarkdownConverter converter = new PptToMarkdownConverter();
            converter.start();
            
            // Create performance config for batch processing
            PptConversionConfig batchConfig = PptConversionConfig.createPerformanceConfig();
            
            // Simulate batch processing
            File[] testFiles = {
                createTestPptFile(),
                createTestPptFile(),
                createTestPptFile()
            };
            
            int successCount = 0;
            int failCount = 0;
            
            for (int i = 0; i < testFiles.length; i++) {
                try {
                    System.out.println("Processing file " + (i + 1) + "/" + testFiles.length);
                    ConversionResult result = converter.convert(testFiles[i]);
                    
                    if (result.getStatus() == ConversionResult.Status.SUCCESS) {
                        successCount++;
                        System.out.println("  ✓ Success");
                    } else {
                        failCount++;
                        System.out.println("  ✗ Failed: " + result.getMessage());
                    }
                } catch (Exception e) {
                    failCount++;
                    System.out.println("  ✗ Error: " + e.getMessage());
                }
            }
            
            System.out.println("Batch processing completed:");
            System.out.println("  - Successful: " + successCount);
            System.out.println("  - Failed: " + failCount);
            
            converter.stop();
            
        } catch (Exception e) {
            System.out.println("✗ Batch processing failed: " + e.getMessage());
        }
        
        System.out.println();
    }
}
