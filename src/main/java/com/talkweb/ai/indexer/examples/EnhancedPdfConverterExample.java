package com.talkweb.ai.indexer.examples;

import com.talkweb.ai.indexer.core.*;
import com.talkweb.ai.indexer.core.impl.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;
import java.util.logging.Logger;

/**
 * Example demonstrating the enhanced PDF to Markdown converter capabilities
 */
public class EnhancedPdfConverterExample {
    
    private static final Logger logger = Logger.getLogger(EnhancedPdfConverterExample.class.getName());
    
    public static void main(String[] args) {
        try {
            // Example 1: Basic conversion with default settings
            basicConversionExample();
            
            // Example 2: High-quality conversion with page splitting
            highQualityConversionExample();
            
            // Example 3: Fast conversion for batch processing
            fastConversionExample();
            
            // Example 4: Custom configuration
            customConfigurationExample();
            
            // Example 5: Configuration from properties
            propertiesConfigurationExample();
            
            // Example 6: Error handling demonstration
            errorHandlingExample();
            
        } catch (Exception e) {
            logger.severe("Example execution failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Example 1: Basic conversion with default settings
     */
    private static void basicConversionExample() throws Exception {
        logger.info("=== Basic Conversion Example ===");
        
        // Create converter with metadata
        PluginMetadata metadata = PluginMetadata.builder()
            .id("pdf-converter-example")
            .name("PDF to Markdown Converter Example")
            .version("1.0.0")
            .description("Example usage of enhanced PDF converter")
            .build();
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter(metadata);
        
        // Initialize and start the converter
        converter.init(new DefaultPluginContext());
        converter.start();
        
        // Convert a PDF file (assuming it exists)
        File pdfFile = new File("sample.pdf");
        if (pdfFile.exists()) {
            ConversionResult result = converter.convert(pdfFile);
            
            if (result.isSuccess()) {
                logger.info("Conversion successful!");
                logger.info("Output: " + result.getMessage());
                logger.info("Content preview: " + result.getContent().substring(0, Math.min(200, result.getContent().length())));
            } else {
                logger.warning("Conversion failed: " + result.getMessage());
            }
        } else {
            logger.info("Sample PDF file not found, skipping basic conversion example");
        }
        
        converter.stop();
    }
    
    /**
     * Example 2: High-quality conversion with page splitting
     */
    private static void highQualityConversionExample() throws Exception {
        logger.info("=== High-Quality Conversion Example ===");
        
        PluginMetadata metadata = PluginMetadata.builder()
            .id("pdf-converter-hq")
            .name("High-Quality PDF Converter")
            .version("1.0.0")
            .build();
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter(metadata);
        
        // Configure for high-quality conversion
        PdfToMarkdownConverter.PdfConversionConfig config = 
            PdfConversionConfigBuilder.createHighQualityConfig();
        
        converter.configure(config);
        converter.init(new DefaultPluginContext());
        converter.start();
        
        logger.info("Configuration: splitByPages=" + config.isSplitByPages() + 
                   ", preserveStructure=" + config.isPreserveStructure() + 
                   ", includeMetadata=" + config.isIncludeMetadata());
        
        // This would split the PDF into separate markdown files for each page
        File pdfFile = new File("document.pdf");
        if (pdfFile.exists()) {
            ConversionResult result = converter.convert(pdfFile);
            logger.info("High-quality conversion result: " + result.getStatus());
        } else {
            logger.info("Document PDF file not found, skipping high-quality conversion example");
        }
        
        converter.stop();
    }
    
    /**
     * Example 3: Fast conversion for batch processing
     */
    private static void fastConversionExample() throws Exception {
        logger.info("=== Fast Conversion Example ===");
        
        PluginMetadata metadata = PluginMetadata.builder()
            .id("pdf-converter-fast")
            .name("Fast PDF Converter")
            .version("1.0.0")
            .build();
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter(metadata);
        
        // Configure for fast conversion
        PdfToMarkdownConverter.PdfConversionConfig config = 
            PdfConversionConfigBuilder.createFastConfig();
        
        converter.configure(config);
        converter.init(new DefaultPluginContext());
        converter.start();
        
        logger.info("Fast configuration: splitByPages=" + config.isSplitByPages() + 
                   ", maxPages=" + config.getMaxPages() + 
                   ", includeMetadata=" + config.isIncludeMetadata());
        
        converter.stop();
    }
    
    /**
     * Example 4: Custom configuration
     */
    private static void customConfigurationExample() throws Exception {
        logger.info("=== Custom Configuration Example ===");
        
        PluginMetadata metadata = PluginMetadata.builder()
            .id("pdf-converter-custom")
            .name("Custom PDF Converter")
            .version("1.0.0")
            .build();
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter(metadata);
        
        // Create custom configuration
        PdfToMarkdownConverter.PdfConversionConfig config = 
            PdfConversionConfigBuilder.newBuilder()
                .splitByPages(true)
                .preserveStructure(true)
                .extractImages(false)
                .handleEncrypted(true)
                .defaultPassword("mypassword")
                .maxPages(50)
                .includeMetadata(true)
                .build();
        
        converter.configure(config);
        converter.init(new DefaultPluginContext());
        converter.start();
        
        logger.info("Custom configuration applied successfully");
        logger.info("Max pages: " + config.getMaxPages());
        logger.info("Handle encrypted: " + config.isHandleEncrypted());
        
        converter.stop();
    }
    
    /**
     * Example 5: Configuration from properties
     */
    private static void propertiesConfigurationExample() throws Exception {
        logger.info("=== Properties Configuration Example ===");
        
        // Create properties
        Properties props = new Properties();
        props.setProperty("pdf.splitByPages", "true");
        props.setProperty("pdf.preserveStructure", "true");
        props.setProperty("pdf.maxPages", "25");
        props.setProperty("pdf.includeMetadata", "true");
        props.setProperty("pdf.handleEncrypted", "false");
        
        // Create configuration from properties
        PdfToMarkdownConverter.PdfConversionConfig config = 
            PdfConversionConfigBuilder.fromProperties(props).build();
        
        PluginMetadata metadata = PluginMetadata.builder()
            .id("pdf-converter-props")
            .name("Properties-based PDF Converter")
            .version("1.0.0")
            .build();
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter(metadata);
        converter.configure(config);
        
        logger.info("Configuration from properties:");
        logger.info("  splitByPages: " + config.isSplitByPages());
        logger.info("  preserveStructure: " + config.isPreserveStructure());
        logger.info("  maxPages: " + config.getMaxPages());
        logger.info("  includeMetadata: " + config.isIncludeMetadata());
        logger.info("  handleEncrypted: " + config.isHandleEncrypted());
    }
    
    /**
     * Example 6: Error handling demonstration
     */
    private static void errorHandlingExample() throws Exception {
        logger.info("=== Error Handling Example ===");
        
        PluginMetadata metadata = PluginMetadata.builder()
            .id("pdf-converter-error")
            .name("Error Handling PDF Converter")
            .version("1.0.0")
            .build();
        
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter(metadata);
        converter.init(new DefaultPluginContext());
        converter.start();
        
        // Test various error scenarios
        try {
            // Test with non-existent file
            File nonExistentFile = new File("non_existent.pdf");
            converter.convert(nonExistentFile);
        } catch (ConversionException e) {
            logger.info("Expected error for non-existent file: " + e.getMessage());
        }
        
        try {
            // Test with null file
            converter.convert(null);
        } catch (ConversionException e) {
            logger.info("Expected error for null file: " + e.getMessage());
        }
        
        // Demonstrate exception types
        PdfConversionException fileNotFound = PdfConversionException.fileNotFound("missing.pdf");
        logger.info("File not found exception: " + fileNotFound.getDetailedMessage());
        logger.info("Error type: " + fileNotFound.getErrorType());
        logger.info("Is recoverable: " + fileNotFound.isRecoverable());
        
        PdfConversionException encrypted = PdfConversionException.encryptedFile("encrypted.pdf");
        logger.info("Encrypted file exception: " + encrypted.getDetailedMessage());
        logger.info("Is recoverable: " + encrypted.isRecoverable());
        
        converter.stop();
    }
    
    /**
     * Simple plugin context implementation for examples
     */
    private static class DefaultPluginContext implements PluginContext {

        @Override
        public ClassLoader getPluginClassLoader() {
            return Thread.currentThread().getContextClassLoader();
        }

        @Override
        public java.util.Properties getConfiguration() {
            return System.getProperties();
        }

        @Override
        public PluginManager getPluginManager() {
            return null; // Not needed for examples
        }

        @Override
        public String getWorkDir() {
            return System.getProperty("user.dir");
        }

        @Override
        public String getTempDir() {
            return System.getProperty("java.io.tmpdir");
        }

        @Override
        public void info(String message, Object... args) {
            if (args.length > 0) {
                logger.info(String.format(message, args));
            } else {
                logger.info(message);
            }
        }

        @Override
        public void warn(String message, Object... args) {
            if (args.length > 0) {
                logger.warning(String.format(message, args));
            } else {
                logger.warning(message);
            }
        }

        @Override
        public void error(String message, Throwable throwable, Object... args) {
            String formattedMessage = args.length > 0 ? String.format(message, args) : message;
            if (throwable != null) {
                logger.severe(formattedMessage + " - " + throwable.getMessage());
            } else {
                logger.severe(formattedMessage);
            }
        }

        @Override
        public void debug(String message, Object... args) {
            String formattedMessage = args.length > 0 ? String.format(message, args) : message;
            logger.info("DEBUG: " + formattedMessage);
        }

        @Override
        public void setSharedObject(String key, Object value) {
            // Not implemented for examples
        }

        @Override
        public java.util.Optional<Object> getSharedObject(String key) {
            return java.util.Optional.empty();
        }
    }
}
