package com.talkweb.ai.indexer.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * AI Configuration for document processing and analysis
 *
 * This is a placeholder configuration for AI features.
 * When Spring AI dependencies are properly configured, this can be expanded
 * to include actual AI service beans.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Configuration
@ConditionalOnProperty(name = "ai.enabled", havingValue = "true", matchIfMissing = false)
public class AiConfiguration {

    @Value("${ai.openai.api-key:}")
    private String openAiApiKey;

    @Value("${ai.openai.base-url:https://api.openai.com}")
    private String openAiBaseUrl;

    @Value("${ai.openai.chat.model:gpt-3.5-turbo}")
    private String chatModel;

    @Value("${ai.openai.embedding.model:text-embedding-ada-002}")
    private String embeddingModel;

    // TODO: Add actual AI service beans when Spring AI dependencies are resolved
    // For now, this serves as a configuration placeholder
}
