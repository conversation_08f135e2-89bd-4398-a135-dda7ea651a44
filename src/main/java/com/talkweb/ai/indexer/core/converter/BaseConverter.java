package com.talkweb.ai.indexer.core.converter;

import com.talkweb.ai.indexer.core.ConversionException;

/**
 * 基础转换器接口，所有转换器的顶层接口
 * 
 * @param <T> 输入类型
 * @param <R> 输出类型
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface BaseConverter<T, R> {
    
    /**
     * 执行转换操作
     * 
     * @param input 输入对象
     * @param context 转换上下文
     * @return 转换结果
     * @throws ConversionException 转换异常
     */
    R convert(T input, ConversionContext context) throws ConversionException;
    
    /**
     * 检查是否支持转换指定的输入
     * 
     * @param input 输入对象
     * @param context 转换上下文
     * @return 如果支持转换返回true，否则返回false
     */
    boolean supports(T input, ConversionContext context);
    
    /**
     * 获取转换器元数据
     * 
     * @return 转换器元数据
     */
    ConversionMetadata getMetadata();
}