package com.talkweb.ai.indexer.core.converter;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 默认的转换选项实现
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class DefaultConversionOptions implements ConversionOptions {
    
    private final Map<String, Object> options;
    
    /**
     * 创建默认的转换选项
     */
    public DefaultConversionOptions() {
        this.options = new HashMap<>();
    }
    
    /**
     * 创建默认的转换选项
     * 
     * @param options 选项映射
     */
    public DefaultConversionOptions(Map<String, Object> options) {
        this.options = new HashMap<>(options);
    }
    
    @Override
    public Optional<Object> getOption(String key) {
        return Optional.ofNullable(options.get(key));
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public <T> T getOption(String key, T defaultValue) {
        Object value = options.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return (T) value;
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }
    
    @Override
    public boolean getBooleanOption(String key, boolean defaultValue) {
        Object value = options.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        
        if (value instanceof String) {
            String strValue = (String) value;
            return "true".equalsIgnoreCase(strValue) || "yes".equalsIgnoreCase(strValue) || "1".equals(strValue);
        }
        
        if (value instanceof Number) {
            return ((Number) value).intValue() != 0;
        }
        
        return defaultValue;
    }
    
    @Override
    public int getIntOption(String key, int defaultValue) {
        Object value = options.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        
        return defaultValue;
    }
    
    @Override
    public String getStringOption(String key, String defaultValue) {
        Object value = options.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        return value.toString();
    }
    
    @Override
    public Map<String, Object> getAllOptions() {
        return Collections.unmodifiableMap(options);
    }
    
    /**
     * 默认的选项构建器实现
     */
    public static class Builder implements ConversionOptions.Builder {
        private final Map<String, Object> options = new HashMap<>();
        
        @Override
        public Builder option(String key, Object value) {
            options.put(key, value);
            return this;
        }
        
        @Override
        public ConversionOptions build() {
            return new DefaultConversionOptions(options);
        }
    }
}