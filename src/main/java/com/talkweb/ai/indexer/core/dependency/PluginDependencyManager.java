package com.talkweb.ai.indexer.core.dependency;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginMetadata;

import java.util.List;
import java.util.Set;

/**
 * 插件依赖管理器接口
 */
public interface PluginDependencyManager {

    /**
     * 解析插件的依赖关系
     * 
     * @param plugin 插件
     * @return 依赖列表
     * @throws PluginDependencyException 如果解析失败
     */
    List<PluginDependency> resolveDependencies(Plugin plugin) throws PluginDependencyException;

    /**
     * 检查插件依赖是否满足
     * 
     * @param plugin 插件
     * @param availablePlugins 可用插件列表
     * @return 依赖检查结果
     */
    DependencyCheckResult checkDependencies(Plugin plugin, List<Plugin> availablePlugins);

    /**
     * 检测循环依赖
     * 
     * @param plugins 插件列表
     * @return 循环依赖检测结果
     */
    CircularDependencyResult detectCircularDependencies(List<Plugin> plugins);

    /**
     * 计算插件加载顺序
     * 
     * @param plugins 插件列表
     * @return 按依赖关系排序的插件列表
     * @throws PluginDependencyException 如果存在循环依赖或无法解析的依赖
     */
    List<Plugin> calculateLoadOrder(List<Plugin> plugins) throws PluginDependencyException;

    /**
     * 获取插件的直接依赖
     * 
     * @param plugin 插件
     * @return 直接依赖列表
     */
    List<PluginDependency> getDirectDependencies(Plugin plugin);

    /**
     * 获取插件的传递依赖
     * 
     * @param plugin 插件
     * @param availablePlugins 可用插件列表
     * @return 传递依赖列表
     */
    List<PluginDependency> getTransitiveDependencies(Plugin plugin, List<Plugin> availablePlugins);

    /**
     * 获取依赖于指定插件的插件列表
     * 
     * @param pluginId 插件ID
     * @param availablePlugins 可用插件列表
     * @return 依赖者列表
     */
    List<Plugin> getDependents(String pluginId, List<Plugin> availablePlugins);

    /**
     * 验证插件版本兼容性
     * 
     * @param dependency 依赖信息
     * @param availablePlugin 可用插件
     * @return 如果兼容返回true
     */
    boolean isVersionCompatible(PluginDependency dependency, Plugin availablePlugin);

    /**
     * 依赖检查结果
     */
    class DependencyCheckResult {
        private final boolean satisfied;
        private final List<PluginDependency> missingDependencies;
        private final List<PluginDependency> incompatibleDependencies;
        private final List<String> warnings;

        public DependencyCheckResult(boolean satisfied, 
                                   List<PluginDependency> missingDependencies,
                                   List<PluginDependency> incompatibleDependencies,
                                   List<String> warnings) {
            this.satisfied = satisfied;
            this.missingDependencies = missingDependencies;
            this.incompatibleDependencies = incompatibleDependencies;
            this.warnings = warnings;
        }

        public boolean isSatisfied() {
            return satisfied;
        }

        public List<PluginDependency> getMissingDependencies() {
            return missingDependencies;
        }

        public List<PluginDependency> getIncompatibleDependencies() {
            return incompatibleDependencies;
        }

        public List<String> getWarnings() {
            return warnings;
        }

        public boolean hasErrors() {
            return !missingDependencies.isEmpty() || !incompatibleDependencies.isEmpty();
        }

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }
    }

    /**
     * 循环依赖检测结果
     */
    class CircularDependencyResult {
        private final boolean hasCircularDependency;
        private final List<List<String>> circularChains;

        public CircularDependencyResult(boolean hasCircularDependency, List<List<String>> circularChains) {
            this.hasCircularDependency = hasCircularDependency;
            this.circularChains = circularChains;
        }

        public boolean hasCircularDependency() {
            return hasCircularDependency;
        }

        public List<List<String>> getCircularChains() {
            return circularChains;
        }
    }
}
