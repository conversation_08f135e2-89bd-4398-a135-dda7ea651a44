package com.talkweb.ai.indexer.core.adapter;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.core.converter.DocumentConverter;

import java.util.Objects;
import java.util.logging.Logger;

/**
 * 转换器插件适配器工厂类
 * 
 * 提供便捷的方法来创建转换器插件适配器，支持自动生成元数据
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConverterPluginAdapterFactory {
    
    private static final Logger logger = Logger.getLogger(ConverterPluginAdapterFactory.class.getName());
    
    /**
     * 创建转换器插件适配器
     * 
     * @param converter 文档转换器
     * @param metadata 插件元数据
     * @return 插件适配器
     */
    public static Plugin createAdapter(DocumentConverter converter, PluginMetadata metadata) {
        Objects.requireNonNull(converter, "Converter cannot be null");
        Objects.requireNonNull(metadata, "Metadata cannot be null");
        
        return new ConverterPluginAdapter(converter, metadata);
    }
    
    /**
     * 创建转换器插件适配器，自动生成元数据
     * 
     * @param converter 文档转换器
     * @param id 插件ID
     * @param name 插件名称
     * @param version 插件版本
     * @param description 插件描述
     * @return 插件适配器
     */
    public static Plugin createAdapter(DocumentConverter converter, String id, String name, 
                                     String version, String description) {
        Objects.requireNonNull(converter, "Converter cannot be null");
        Objects.requireNonNull(id, "Plugin ID cannot be null");
        Objects.requireNonNull(name, "Plugin name cannot be null");
        Objects.requireNonNull(version, "Plugin version cannot be null");
        
        PluginMetadata metadata = PluginMetadata.builder()
                .id(id)
                .name(name)
                .version(version)
                .description(description != null ? description : "")
                .provider("System")
                .className(converter.getClass().getName())
                .build();
        
        return createAdapter(converter, metadata);
    }
    
    /**
     * 创建转换器插件适配器，使用默认元数据
     * 
     * @param converter 文档转换器
     * @return 插件适配器
     */
    public static Plugin createAdapter(DocumentConverter converter) {
        Objects.requireNonNull(converter, "Converter cannot be null");
        
        String className = converter.getClass().getSimpleName();
        String id = generateId(className);
        String name = generateName(className);
        String version = "1.0.0";
        String description = "Auto-generated adapter for " + className;
        
        return createAdapter(converter, id, name, version, description);
    }
    
    /**
     * 从转换器类名生成插件ID
     * 
     * @param className 类名
     * @return 插件ID
     */
    private static String generateId(String className) {
        // 将类名转换为kebab-case格式
        return className
                .replaceAll("([a-z])([A-Z])", "$1-$2")
                .toLowerCase()
                .replace("converter", "")
                .replace("--", "-")
                .replaceAll("^-|-$", "");
    }
    
    /**
     * 从转换器类名生成插件名称
     * 
     * @param className 类名
     * @return 插件名称
     */
    private static String generateName(String className) {
        // 将类名转换为友好的显示名称
        return className
                .replaceAll("([a-z])([A-Z])", "$1 $2")
                .replace("To Markdown Converter", " to Markdown Converter")
                .trim();
    }
    
    /**
     * 检查转换器是否已经是插件适配器
     * 
     * @param converter 转换器
     * @return 如果是适配器返回true
     */
    public static boolean isAdapter(DocumentConverter converter) {
        return converter instanceof ConverterPluginAdapter;
    }
    
    /**
     * 从插件适配器中提取原始转换器
     * 
     * @param plugin 插件
     * @return 原始转换器，如果不是适配器则返回null
     */
    public static DocumentConverter extractConverter(Plugin plugin) {
        if (plugin instanceof ConverterPluginAdapter) {
            return ((ConverterPluginAdapter) plugin).getConverter();
        }
        return null;
    }
    
    /**
     * 创建批量适配器
     * 
     * @param converters 转换器数组
     * @return 插件适配器数组
     */
    public static Plugin[] createAdapters(DocumentConverter... converters) {
        if (converters == null || converters.length == 0) {
            return new Plugin[0];
        }
        
        Plugin[] adapters = new Plugin[converters.length];
        for (int i = 0; i < converters.length; i++) {
            adapters[i] = createAdapter(converters[i]);
        }
        
        return adapters;
    }
    
    /**
     * 验证适配器配置
     * 
     * @param converter 转换器
     * @param metadata 元数据
     * @throws IllegalArgumentException 如果配置无效
     */
    public static void validateAdapterConfig(DocumentConverter converter, PluginMetadata metadata) {
        Objects.requireNonNull(converter, "Converter cannot be null");
        Objects.requireNonNull(metadata, "Metadata cannot be null");
        
        if (metadata.getId() == null || metadata.getId().trim().isEmpty()) {
            throw new IllegalArgumentException("Plugin ID cannot be null or empty");
        }
        
        if (metadata.getName() == null || metadata.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("Plugin name cannot be null or empty");
        }
        
        if (metadata.getVersion() == null || metadata.getVersion().trim().isEmpty()) {
            throw new IllegalArgumentException("Plugin version cannot be null or empty");
        }
        
        if (metadata.getClassName() == null || metadata.getClassName().trim().isEmpty()) {
            throw new IllegalArgumentException("Plugin class name cannot be null or empty");
        }
        
        // 验证类名是否匹配
        if (!converter.getClass().getName().equals(metadata.getClassName())) {
            logger.warning("Converter class name does not match metadata class name: " +
                    converter.getClass().getName() + " vs " + metadata.getClassName());
        }
    }
}
