package com.talkweb.ai.indexer.core.converter;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 转换器元数据，包含转换器的名称、版本、描述等信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConversionMetadata {
    
    private final String name;
    private final String version;
    private final String description;
    private final Map<String, Object> attributes;
    
    /**
     * 创建转换器元数据
     * 
     * @param name 转换器名称
     * @param version 转换器版本
     * @param description 转换器描述
     */
    public ConversionMetadata(String name, String version, String description) {
        this.name = name;
        this.version = version;
        this.description = description;
        this.attributes = new HashMap<>();
    }
    
    /**
     * 创建转换器元数据
     * 
     * @param name 转换器名称
     * @param version 转换器版本
     * @param description 转换器描述
     * @param attributes 附加属性
     */
    public ConversionMetadata(String name, String version, String description, Map<String, Object> attributes) {
        this.name = name;
        this.version = version;
        this.description = description;
        this.attributes = new HashMap<>(attributes);
    }
    
    /**
     * 获取转换器名称
     * 
     * @return 转换器名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取转换器版本
     * 
     * @return 转换器版本
     */
    public String getVersion() {
        return version;
    }
    
    /**
     * 获取转换器描述
     * 
     * @return 转换器描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取附加属性
     * 
     * @return 不可变的附加属性映射
     */
    public Map<String, Object> getAttributes() {
        return Collections.unmodifiableMap(attributes);
    }
    
    /**
     * 获取指定的附加属性
     * 
     * @param key 属性键
     * @return 属性值，如果不存在则返回null
     */
    public Object getAttribute(String key) {
        return attributes.get(key);
    }
    
    /**
     * 获取指定的附加属性，如果不存在则返回默认值
     * 
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值，如果不存在则返回默认值
     */
    public Object getAttribute(String key, Object defaultValue) {
        return attributes.getOrDefault(key, defaultValue);
    }
    
    /**
     * 添加附加属性
     * 
     * @param key 属性键
     * @param value 属性值
     * @return 当前元数据对象，用于链式调用
     */
    public ConversionMetadata addAttribute(String key, Object value) {
        attributes.put(key, value);
        return this;
    }
    
    /**
     * 创建元数据构建器
     * 
     * @param name 转换器名称
     * @return 元数据构建器
     */
    public static Builder builder(String name) {
        return new Builder(name);
    }
    
    /**
     * 元数据构建器
     */
    public static class Builder {
        private final String name;
        private String version = "1.0";
        private String description = "";
        private final Map<String, Object> attributes = new HashMap<>();
        
        private Builder(String name) {
            this.name = name;
        }
        
        /**
         * 设置版本
         * 
         * @param version 版本
         * @return 当前构建器
         */
        public Builder version(String version) {
            this.version = version;
            return this;
        }
        
        /**
         * 设置描述
         * 
         * @param description 描述
         * @return 当前构建器
         */
        public Builder description(String description) {
            this.description = description;
            return this;
        }
        
        /**
         * 添加属性
         * 
         * @param key 属性键
         * @param value 属性值
         * @return 当前构建器
         */
        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }
        
        /**
         * 构建元数据对象
         * 
         * @return 元数据对象
         */
        public ConversionMetadata build() {
            return new ConversionMetadata(name, version, description, attributes);
        }
    }
}