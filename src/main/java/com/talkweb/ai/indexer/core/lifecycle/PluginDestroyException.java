package com.talkweb.ai.indexer.core.lifecycle;

import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginState;

/**
 * 插件销毁异常
 */
public class PluginDestroyException extends PluginException {
    
    private final String pluginId;

    public PluginDestroyException(String message, String pluginId) {
        super(message, PluginState.FAILED);
        this.pluginId = pluginId;
    }

    public PluginDestroyException(String message, String pluginId, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
    }

    public String getPluginId() {
        return pluginId;
    }

    public static PluginDestroyException resourceCleanupError(String pluginId, String resource, Throwable cause) {
        return new PluginDestroyException(
            "Failed to cleanup resource during plugin destruction: " + resource + " (Plugin: " + pluginId + ")",
            pluginId,
            cause
        );
    }

    public static PluginDestroyException timeoutError(String pluginId, long timeoutMs) {
        return new PluginDestroyException(
            "Plugin destroy timeout after " + timeoutMs + "ms (Plugin: " + pluginId + ")",
            pluginId
        );
    }
}
