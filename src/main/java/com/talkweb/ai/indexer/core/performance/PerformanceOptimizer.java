package com.talkweb.ai.indexer.core.performance;

import com.talkweb.ai.indexer.core.util.ConversionCacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Performance optimizer for document conversion operations
 * 
 * This class provides various performance optimization strategies including:
 * - Dynamic cache sizing based on memory usage
 * - Memory pressure monitoring
 * - Automatic garbage collection suggestions
 * - Performance metrics collection
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class PerformanceOptimizer {
    
    private static final Logger logger = LoggerFactory.getLogger(PerformanceOptimizer.class);
    
    private static final double MEMORY_PRESSURE_THRESHOLD = 0.8; // 80% memory usage
    private static final double CRITICAL_MEMORY_THRESHOLD = 0.9; // 90% memory usage
    private static final long MONITORING_INTERVAL_SECONDS = 30;
    
    private final MemoryMXBean memoryBean;
    private final ScheduledExecutorService scheduler;
    private final PerformanceMetrics metrics;
    
    private volatile boolean monitoringEnabled = true;
    private volatile long lastGcSuggestion = 0;
    
    public PerformanceOptimizer() {
        this.memoryBean = ManagementFactory.getMemoryMXBean();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "PerformanceOptimizer");
            t.setDaemon(true);
            return t;
        });
        this.metrics = new PerformanceMetrics();
        
        startMonitoring();
    }
    
    /**
     * Starts performance monitoring
     */
    private void startMonitoring() {
        scheduler.scheduleAtFixedRate(this::monitorPerformance, 
            MONITORING_INTERVAL_SECONDS, MONITORING_INTERVAL_SECONDS, TimeUnit.SECONDS);
        logger.info("Performance monitoring started with interval: {} seconds", MONITORING_INTERVAL_SECONDS);
    }
    
    /**
     * Monitors system performance and applies optimizations
     */
    private void monitorPerformance() {
        if (!monitoringEnabled) {
            return;
        }
        
        try {
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            double memoryUsageRatio = (double) heapUsage.getUsed() / heapUsage.getMax();
            
            metrics.updateMemoryUsage(memoryUsageRatio);
            
            if (memoryUsageRatio > CRITICAL_MEMORY_THRESHOLD) {
                handleCriticalMemoryPressure();
            } else if (memoryUsageRatio > MEMORY_PRESSURE_THRESHOLD) {
                handleMemoryPressure();
            }
            
            // Optimize cache sizes based on memory usage
            optimizeCacheSizes(memoryUsageRatio);
            
        } catch (Exception e) {
            logger.warn("Error during performance monitoring", e);
        }
    }
    
    /**
     * Handles critical memory pressure situations
     */
    private void handleCriticalMemoryPressure() {
        logger.warn("Critical memory pressure detected, applying aggressive optimizations");
        
        // Clear all caches
        ConversionCacheManager.clearAllCaches();
        
        // Suggest garbage collection
        suggestGarbageCollection();
        
        metrics.incrementCriticalMemoryEvents();
    }
    
    /**
     * Handles moderate memory pressure
     */
    private void handleMemoryPressure() {
        logger.info("Memory pressure detected, applying optimizations");
        
        // Reduce cache sizes
        ConversionCacheManager.reduceCacheSizes(0.5); // Reduce to 50% of current size
        
        metrics.incrementMemoryPressureEvents();
    }
    
    /**
     * Optimizes cache sizes based on current memory usage
     */
    private void optimizeCacheSizes(double memoryUsageRatio) {
        if (memoryUsageRatio < 0.5) {
            // Low memory usage, can increase cache sizes
            ConversionCacheManager.increaseCacheSizes(1.2); // Increase by 20%
        } else if (memoryUsageRatio > 0.7) {
            // High memory usage, reduce cache sizes
            ConversionCacheManager.reduceCacheSizes(0.9); // Reduce by 10%
        }
    }
    
    /**
     * Suggests garbage collection if not done recently
     */
    private void suggestGarbageCollection() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastGcSuggestion > 60000) { // Only suggest GC once per minute
            System.gc();
            lastGcSuggestion = currentTime;
            logger.debug("Garbage collection suggested due to memory pressure");
        }
    }
    
    /**
     * Gets current performance metrics
     */
    public PerformanceMetrics getMetrics() {
        return metrics;
    }
    
    /**
     * Enables or disables performance monitoring
     */
    public void setMonitoringEnabled(boolean enabled) {
        this.monitoringEnabled = enabled;
        logger.info("Performance monitoring {}", enabled ? "enabled" : "disabled");
    }
    
    /**
     * Optimizes conversion settings for large files
     */
    public ConversionOptimizationSettings optimizeForLargeFile(long fileSizeBytes) {
        ConversionOptimizationSettings settings = new ConversionOptimizationSettings();
        
        if (fileSizeBytes > 100 * 1024 * 1024) { // > 100MB
            settings.setUseStreamProcessing(true);
            settings.setChunkSize(1024 * 1024); // 1MB chunks
            settings.setEnableCaching(false); // Disable caching for very large files
            settings.setMaxMemoryUsage(512 * 1024 * 1024); // 512MB max
        } else if (fileSizeBytes > 10 * 1024 * 1024) { // > 10MB
            settings.setUseStreamProcessing(true);
            settings.setChunkSize(512 * 1024); // 512KB chunks
            settings.setEnableCaching(true);
            settings.setMaxMemoryUsage(256 * 1024 * 1024); // 256MB max
        } else {
            settings.setUseStreamProcessing(false);
            settings.setEnableCaching(true);
            settings.setMaxMemoryUsage(128 * 1024 * 1024); // 128MB max
        }
        
        return settings;
    }
    
    /**
     * Shuts down the performance optimizer
     */
    public void shutdown() {
        monitoringEnabled = false;
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("Performance optimizer shut down");
    }
    
    /**
     * Performance metrics holder
     */
    public static class PerformanceMetrics {
        private volatile double currentMemoryUsage = 0.0;
        private volatile double maxMemoryUsage = 0.0;
        private volatile long memoryPressureEvents = 0;
        private volatile long criticalMemoryEvents = 0;
        private volatile long totalOptimizations = 0;
        
        public void updateMemoryUsage(double usage) {
            this.currentMemoryUsage = usage;
            if (usage > maxMemoryUsage) {
                this.maxMemoryUsage = usage;
            }
        }
        
        public void incrementMemoryPressureEvents() {
            this.memoryPressureEvents++;
            this.totalOptimizations++;
        }
        
        public void incrementCriticalMemoryEvents() {
            this.criticalMemoryEvents++;
            this.totalOptimizations++;
        }
        
        // Getters
        public double getCurrentMemoryUsage() { return currentMemoryUsage; }
        public double getMaxMemoryUsage() { return maxMemoryUsage; }
        public long getMemoryPressureEvents() { return memoryPressureEvents; }
        public long getCriticalMemoryEvents() { return criticalMemoryEvents; }
        public long getTotalOptimizations() { return totalOptimizations; }
        
        @Override
        public String toString() {
            return String.format("PerformanceMetrics{currentMemory=%.2f%%, maxMemory=%.2f%%, pressureEvents=%d, criticalEvents=%d, totalOptimizations=%d}",
                    currentMemoryUsage * 100, maxMemoryUsage * 100, memoryPressureEvents, criticalMemoryEvents, totalOptimizations);
        }
    }
    
    /**
     * Conversion optimization settings
     */
    public static class ConversionOptimizationSettings {
        private boolean useStreamProcessing = false;
        private int chunkSize = 64 * 1024; // 64KB default
        private boolean enableCaching = true;
        private long maxMemoryUsage = 128 * 1024 * 1024; // 128MB default
        
        // Getters and setters
        public boolean isUseStreamProcessing() { return useStreamProcessing; }
        public void setUseStreamProcessing(boolean useStreamProcessing) { this.useStreamProcessing = useStreamProcessing; }
        
        public int getChunkSize() { return chunkSize; }
        public void setChunkSize(int chunkSize) { this.chunkSize = chunkSize; }
        
        public boolean isEnableCaching() { return enableCaching; }
        public void setEnableCaching(boolean enableCaching) { this.enableCaching = enableCaching; }
        
        public long getMaxMemoryUsage() { return maxMemoryUsage; }
        public void setMaxMemoryUsage(long maxMemoryUsage) { this.maxMemoryUsage = maxMemoryUsage; }
    }
}
