package com.talkweb.ai.indexer.core.lifecycle;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginState;

/**
 * 插件生命周期监听器接口
 * 用于监听插件状态变化事件
 */
public interface PluginLifecycleListener {

    /**
     * 插件状态变化前调用
     * 
     * @param plugin 插件实例
     * @param fromState 原状态
     * @param toState 目标状态
     * @return 如果允许状态变化返回true，否则返回false
     */
    default boolean beforeStateChange(Plugin plugin, PluginState fromState, PluginState toState) {
        return true;
    }

    /**
     * 插件状态变化后调用
     * 
     * @param plugin 插件实例
     * @param fromState 原状态
     * @param toState 新状态
     */
    default void afterStateChange(Plugin plugin, PluginState fromState, PluginState toState) {
        // 默认空实现
    }

    /**
     * 插件初始化开始
     * 
     * @param plugin 插件实例
     */
    default void onInitializationStarted(Plugin plugin) {
        // 默认空实现
    }

    /**
     * 插件初始化完成
     * 
     * @param plugin 插件实例
     */
    default void onInitializationCompleted(Plugin plugin) {
        // 默认空实现
    }

    /**
     * 插件初始化失败
     * 
     * @param plugin 插件实例
     * @param error 错误信息
     */
    default void onInitializationFailed(Plugin plugin, Throwable error) {
        // 默认空实现
    }

    /**
     * 插件启动开始
     * 
     * @param plugin 插件实例
     */
    default void onStartStarted(Plugin plugin) {
        // 默认空实现
    }

    /**
     * 插件启动完成
     * 
     * @param plugin 插件实例
     */
    default void onStartCompleted(Plugin plugin) {
        // 默认空实现
    }

    /**
     * 插件启动失败
     * 
     * @param plugin 插件实例
     * @param error 错误信息
     */
    default void onStartFailed(Plugin plugin, Throwable error) {
        // 默认空实现
    }

    /**
     * 插件停止开始
     * 
     * @param plugin 插件实例
     */
    default void onStopStarted(Plugin plugin) {
        // 默认空实现
    }

    /**
     * 插件停止完成
     * 
     * @param plugin 插件实例
     */
    default void onStopCompleted(Plugin plugin) {
        // 默认空实现
    }

    /**
     * 插件停止失败
     * 
     * @param plugin 插件实例
     * @param error 错误信息
     */
    default void onStopFailed(Plugin plugin, Throwable error) {
        // 默认空实现
    }

    /**
     * 插件销毁开始
     * 
     * @param plugin 插件实例
     */
    default void onDestroyStarted(Plugin plugin) {
        // 默认空实现
    }

    /**
     * 插件销毁完成
     * 
     * @param plugin 插件实例
     */
    default void onDestroyCompleted(Plugin plugin) {
        // 默认空实现
    }

    /**
     * 插件销毁失败
     * 
     * @param plugin 插件实例
     * @param error 错误信息
     */
    default void onDestroyFailed(Plugin plugin, Throwable error) {
        // 默认空实现
    }
}
