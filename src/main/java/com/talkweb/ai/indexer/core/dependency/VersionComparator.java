package com.talkweb.ai.indexer.core.dependency;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 版本比较工具类
 */
public class VersionComparator {
    
    private static final Pattern VERSION_PATTERN = Pattern.compile("^(\\d+)(?:\\.(\\d+))?(?:\\.(\\d+))?(?:-(.+))?$");
    
    /**
     * 检查版本是否兼容
     * 
     * @param requiredVersion 需要的版本（可以包含范围表达式）
     * @param availableVersion 可用的版本
     * @return 如果兼容返回true
     */
    public static boolean isCompatible(String requiredVersion, String availableVersion) {
        if (requiredVersion == null || availableVersion == null) {
            return false;
        }
        
        // 处理版本范围表达式
        if (requiredVersion.contains("-")) {
            return handleVersionRange(requiredVersion, availableVersion);
        }
        
        // 处理前缀匹配（如 "1.0+" 表示 1.0 及以上版本）
        if (requiredVersion.endsWith("+")) {
            String baseVersion = requiredVersion.substring(0, requiredVersion.length() - 1);
            return compareVersions(availableVersion, baseVersion) >= 0;
        }
        
        // 处理通配符（如 "1.0.*" 表示 1.0.x 系列）
        if (requiredVersion.contains("*")) {
            return matchesWildcard(requiredVersion, availableVersion);
        }
        
        // 精确匹配
        return requiredVersion.equals(availableVersion);
    }
    
    /**
     * 比较两个版本号
     * 
     * @param version1 版本1
     * @param version2 版本2
     * @return 负数表示version1 < version2，0表示相等，正数表示version1 > version2
     */
    public static int compareVersions(String version1, String version2) {
        if (version1 == null && version2 == null) {
            return 0;
        }
        if (version1 == null) {
            return -1;
        }
        if (version2 == null) {
            return 1;
        }
        
        VersionInfo v1 = parseVersion(version1);
        VersionInfo v2 = parseVersion(version2);
        
        // 比较主版本号
        int result = Integer.compare(v1.major, v2.major);
        if (result != 0) {
            return result;
        }
        
        // 比较次版本号
        result = Integer.compare(v1.minor, v2.minor);
        if (result != 0) {
            return result;
        }
        
        // 比较修订版本号
        result = Integer.compare(v1.patch, v2.patch);
        if (result != 0) {
            return result;
        }
        
        // 比较预发布版本
        return comparePreRelease(v1.preRelease, v2.preRelease);
    }
    
    /**
     * 处理版本范围
     */
    private static boolean handleVersionRange(String requiredVersion, String availableVersion) {
        // 简单的范围处理，如 "1.0-2.0"
        String[] parts = requiredVersion.split("-");
        if (parts.length == 2) {
            String minVersion = parts[0].trim();
            String maxVersion = parts[1].trim();
            
            return compareVersions(availableVersion, minVersion) >= 0 &&
                   compareVersions(availableVersion, maxVersion) <= 0;
        }
        
        return false;
    }
    
    /**
     * 处理通配符匹配
     */
    private static boolean matchesWildcard(String pattern, String version) {
        String regex = pattern.replace(".", "\\.")
                             .replace("*", "\\d+");
        return version.matches(regex);
    }
    
    /**
     * 解析版本字符串
     */
    private static VersionInfo parseVersion(String version) {
        Matcher matcher = VERSION_PATTERN.matcher(version);
        if (!matcher.matches()) {
            // 如果不匹配标准格式，尝试简单解析
            return parseSimpleVersion(version);
        }
        
        int major = Integer.parseInt(matcher.group(1));
        int minor = matcher.group(2) != null ? Integer.parseInt(matcher.group(2)) : 0;
        int patch = matcher.group(3) != null ? Integer.parseInt(matcher.group(3)) : 0;
        String preRelease = matcher.group(4);
        
        return new VersionInfo(major, minor, patch, preRelease);
    }
    
    /**
     * 简单版本解析（用于不标准的版本格式）
     */
    private static VersionInfo parseSimpleVersion(String version) {
        String[] parts = version.split("\\.");
        int major = parts.length > 0 ? parseIntSafely(parts[0]) : 0;
        int minor = parts.length > 1 ? parseIntSafely(parts[1]) : 0;
        int patch = parts.length > 2 ? parseIntSafely(parts[2]) : 0;
        
        return new VersionInfo(major, minor, patch, null);
    }
    
    /**
     * 安全地解析整数
     */
    private static int parseIntSafely(String str) {
        try {
            return Integer.parseInt(str.replaceAll("[^\\d]", ""));
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 比较预发布版本
     */
    private static int comparePreRelease(String pre1, String pre2) {
        if (pre1 == null && pre2 == null) {
            return 0;
        }
        if (pre1 == null) {
            return 1; // 正式版本 > 预发布版本
        }
        if (pre2 == null) {
            return -1; // 预发布版本 < 正式版本
        }
        
        return pre1.compareTo(pre2);
    }
    
    /**
     * 版本信息内部类
     */
    private static class VersionInfo {
        final int major;
        final int minor;
        final int patch;
        final String preRelease;
        
        VersionInfo(int major, int minor, int patch, String preRelease) {
            this.major = major;
            this.minor = minor;
            this.patch = patch;
            this.preRelease = preRelease;
        }
    }
}
