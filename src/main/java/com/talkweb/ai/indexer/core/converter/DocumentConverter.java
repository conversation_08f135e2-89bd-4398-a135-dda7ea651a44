package com.talkweb.ai.indexer.core.converter;

import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.ConversionResult;

import java.io.File;
import java.util.Set;

/**
 * 文档转换器接口，继承自BaseConverter，用于将文件转换为Markdown
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface DocumentConverter extends BaseConverter<File, ConversionResult> {
    
    /**
     * 获取支持的文件扩展名集合
     * 
     * @return 支持的文件扩展名集合
     */
    Set<String> getSupportedExtensions();
    
    /**
     * 获取转换器能力描述
     * 
     * @return 转换器能力描述
     */
    ConversionCapabilities getCapabilities();
    
    /**
     * 检查是否支持指定的文件扩展名
     * 
     * @param extension 文件扩展名
     * @return 如果支持返回true，否则返回false
     */
    default boolean supportsExtension(String extension) {
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        
        String normalizedExtension = extension.toLowerCase();
        if (normalizedExtension.startsWith(".")) {
            normalizedExtension = normalizedExtension.substring(1);
        }
        
        return getSupportedExtensions().contains(normalizedExtension);
    }
    
    /**
     * 执行转换操作（兼容旧接口）
     * 
     * @param input 输入文件
     * @return 转换结果
     * @throws ConversionException 转换异常
     * @deprecated 使用 {@link #convert(File, ConversionContext)} 代替
     */
    @Deprecated
    default ConversionResult convert(File input) throws ConversionException {
        return convert(input, ConversionContext.builder().build());
    }
    
    /**
     * 释放资源
     */
    default void destroy() {
        // 默认实现不做任何操作
    }
}