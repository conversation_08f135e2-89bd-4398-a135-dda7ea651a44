package com.talkweb.ai.indexer.core.lifecycle;

import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginState;

/**
 * 插件初始化异常
 */
public class PluginInitException extends PluginException {
    
    private final String pluginId;

    public PluginInitException(String message, String pluginId) {
        super(message, PluginState.FAILED);
        this.pluginId = pluginId;
    }

    public PluginInitException(String message, String pluginId, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginId = pluginId;
    }

    public String getPluginId() {
        return pluginId;
    }

    public static PluginInitException contextError(String pluginId, String reason) {
        return new PluginInitException(
            "Plugin initialization failed due to context error: " + reason + " (Plugin: " + pluginId + ")",
            pluginId
        );
    }

    public static PluginInitException dependencyError(String pluginId, String missingDependency) {
        return new PluginInitException(
            "Plugin initialization failed due to missing dependency: " + missingDependency + " (Plugin: " + pluginId + ")",
            pluginId
        );
    }

    public static PluginInitException configurationError(String pluginId, String configError) {
        return new PluginInitException(
            "Plugin initialization failed due to configuration error: " + configError + " (Plugin: " + pluginId + ")",
            pluginId
        );
    }
}
