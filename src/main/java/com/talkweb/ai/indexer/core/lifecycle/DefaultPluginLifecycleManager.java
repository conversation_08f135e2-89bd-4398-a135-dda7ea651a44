package com.talkweb.ai.indexer.core.lifecycle;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginContext;
import com.talkweb.ai.indexer.core.PluginState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 默认插件生命周期管理器实现
 */
public class DefaultPluginLifecycleManager implements PluginLifecycleManager {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultPluginLifecycleManager.class);
    
    private final List<PluginLifecycleListener> listeners = new CopyOnWriteArrayList<>();
    
    // 定义有效的状态转换
    private static final Map<PluginState, Set<PluginState>> VALID_TRANSITIONS = new HashMap<>();
    
    static {
        VALID_TRANSITIONS.put(PluginState.REGISTERED, Set.of(PluginState.INITIALIZING, PluginState.FAILED));
        VALID_TRANSITIONS.put(PluginState.LOADED, Set.of(PluginState.INITIALIZING, PluginState.FAILED));
        VALID_TRANSITIONS.put(PluginState.INITIALIZING, Set.of(PluginState.READY, PluginState.FAILED));
        VALID_TRANSITIONS.put(PluginState.READY, Set.of(PluginState.RUNNING, PluginState.DESTROYING, PluginState.FAILED));
        VALID_TRANSITIONS.put(PluginState.RUNNING, Set.of(PluginState.STOPPED, PluginState.DESTROYING, PluginState.FAILED));
        VALID_TRANSITIONS.put(PluginState.STOPPED, Set.of(PluginState.RUNNING, PluginState.DESTROYING, PluginState.FAILED));
        VALID_TRANSITIONS.put(PluginState.DESTROYING, Set.of(PluginState.DESTROYED, PluginState.FAILED));
        VALID_TRANSITIONS.put(PluginState.DESTROYED, Set.of());
        VALID_TRANSITIONS.put(PluginState.FAILED, Set.of(PluginState.DESTROYING));
    }

    @Override
    public void initializePlugin(Plugin plugin, PluginContext context) throws PluginInitException {
        String pluginId = plugin.getMetadata().getId();
        PluginState currentState = plugin.getState();
        
        log.debug("Initializing plugin: {} (current state: {})", pluginId, currentState);
        
        // 检查状态转换是否有效
        if (!isValidStateTransition(currentState, PluginState.INITIALIZING)) {
            throw PluginInitException.contextError(pluginId, 
                "Invalid state transition from " + currentState + " to INITIALIZING");
        }
        
        // 通知监听器
        notifyListeners(l -> l.onInitializationStarted(plugin));
        
        try {
            // 检查状态变化是否被允许
            if (!notifyBeforeStateChange(plugin, currentState, PluginState.INITIALIZING)) {
                throw PluginInitException.contextError(pluginId, "State change was rejected by listener");
            }
            
            // 执行初始化
            plugin.init(context);
            
            // 通知状态变化
            notifyAfterStateChange(plugin, currentState, plugin.getState());
            notifyListeners(l -> l.onInitializationCompleted(plugin));
            
            log.info("Successfully initialized plugin: {}", pluginId);
            
        } catch (PluginInitException e) {
            notifyListeners(l -> l.onInitializationFailed(plugin, e));
            throw e;
        } catch (Exception e) {
            notifyListeners(l -> l.onInitializationFailed(plugin, e));
            throw new PluginInitException("Plugin initialization failed: " + e.getMessage(), pluginId, e);
        }
    }

    @Override
    public void startPlugin(Plugin plugin) throws PluginStartException {
        String pluginId = plugin.getMetadata().getId();
        PluginState currentState = plugin.getState();
        
        log.debug("Starting plugin: {} (current state: {})", pluginId, currentState);
        
        // 检查状态是否允许启动
        if (currentState != PluginState.READY && currentState != PluginState.STOPPED) {
            throw PluginStartException.invalidState(pluginId, currentState);
        }
        
        // 通知监听器
        notifyListeners(l -> l.onStartStarted(plugin));
        
        try {
            // 检查状态变化是否被允许
            if (!notifyBeforeStateChange(plugin, currentState, PluginState.RUNNING)) {
                throw PluginStartException.resourceError(pluginId, "State change was rejected by listener");
            }
            
            // 执行启动
            plugin.start();
            
            // 通知状态变化
            notifyAfterStateChange(plugin, currentState, plugin.getState());
            notifyListeners(l -> l.onStartCompleted(plugin));
            
            log.info("Successfully started plugin: {}", pluginId);
            
        } catch (PluginStartException e) {
            notifyListeners(l -> l.onStartFailed(plugin, e));
            throw e;
        } catch (Exception e) {
            notifyListeners(l -> l.onStartFailed(plugin, e));
            throw new PluginStartException("Plugin start failed: " + e.getMessage(), pluginId, e);
        }
    }

    @Override
    public void stopPlugin(Plugin plugin) throws PluginStopException {
        String pluginId = plugin.getMetadata().getId();
        PluginState currentState = plugin.getState();
        
        log.debug("Stopping plugin: {} (current state: {})", pluginId, currentState);
        
        // 检查状态是否允许停止
        if (currentState != PluginState.RUNNING) {
            throw PluginStopException.invalidState(pluginId, currentState);
        }
        
        // 通知监听器
        notifyListeners(l -> l.onStopStarted(plugin));
        
        try {
            // 检查状态变化是否被允许
            if (!notifyBeforeStateChange(plugin, currentState, PluginState.STOPPED)) {
                throw PluginStopException.timeoutError(pluginId, 0);
            }
            
            // 执行停止
            plugin.stop();
            
            // 通知状态变化
            notifyAfterStateChange(plugin, currentState, plugin.getState());
            notifyListeners(l -> l.onStopCompleted(plugin));
            
            log.info("Successfully stopped plugin: {}", pluginId);
            
        } catch (PluginStopException e) {
            notifyListeners(l -> l.onStopFailed(plugin, e));
            throw e;
        } catch (Exception e) {
            notifyListeners(l -> l.onStopFailed(plugin, e));
            throw new PluginStopException("Plugin stop failed: " + e.getMessage(), pluginId, e);
        }
    }

    @Override
    public void destroyPlugin(Plugin plugin) throws PluginDestroyException {
        String pluginId = plugin.getMetadata().getId();
        PluginState currentState = plugin.getState();
        
        log.debug("Destroying plugin: {} (current state: {})", pluginId, currentState);
        
        // 通知监听器
        notifyListeners(l -> l.onDestroyStarted(plugin));
        
        try {
            // 如果插件正在运行，先停止它
            if (currentState == PluginState.RUNNING) {
                stopPlugin(plugin);
            }
            
            // 检查状态变化是否被允许
            if (!notifyBeforeStateChange(plugin, plugin.getState(), PluginState.DESTROYING)) {
                throw PluginDestroyException.timeoutError(pluginId, 0);
            }
            
            // 执行销毁
            plugin.destroy();
            
            // 通知状态变化
            notifyAfterStateChange(plugin, currentState, plugin.getState());
            notifyListeners(l -> l.onDestroyCompleted(plugin));
            
            log.info("Successfully destroyed plugin: {}", pluginId);
            
        } catch (PluginDestroyException e) {
            notifyListeners(l -> l.onDestroyFailed(plugin, e));
            throw e;
        } catch (PluginStopException e) {
            // 如果停止插件失败，包装为销毁异常
            notifyListeners(l -> l.onDestroyFailed(plugin, e));
            throw new PluginDestroyException("Plugin destroy failed during stop: " + e.getMessage(), pluginId, e);
        } catch (Exception e) {
            notifyListeners(l -> l.onDestroyFailed(plugin, e));
            throw new PluginDestroyException("Plugin destroy failed: " + e.getMessage(), pluginId, e);
        }
    }

    @Override
    public BatchOperationResult initializePlugins(Collection<Plugin> plugins, PluginContext context) {
        return performBatchOperation(plugins, "initialize", 
            plugin -> initializePlugin(plugin, context));
    }

    @Override
    public BatchOperationResult startPlugins(Collection<Plugin> plugins) {
        return performBatchOperation(plugins, "start", this::startPlugin);
    }

    @Override
    public BatchOperationResult stopPlugins(Collection<Plugin> plugins) {
        return performBatchOperation(plugins, "stop", this::stopPlugin);
    }

    @Override
    public BatchOperationResult destroyPlugins(Collection<Plugin> plugins) {
        return performBatchOperation(plugins, "destroy", this::destroyPlugin);
    }

    @Override
    public boolean isValidStateTransition(PluginState currentState, PluginState targetState) {
        Set<PluginState> validTargets = VALID_TRANSITIONS.get(currentState);
        return validTargets != null && validTargets.contains(targetState);
    }

    @Override
    public List<PluginState> getNextValidStates(PluginState currentState) {
        Set<PluginState> validStates = VALID_TRANSITIONS.get(currentState);
        return validStates != null ? new ArrayList<>(validStates) : Collections.emptyList();
    }

    @Override
    public void addLifecycleListener(PluginLifecycleListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
            log.debug("Added lifecycle listener: {}", listener.getClass().getSimpleName());
        }
    }

    @Override
    public void removeLifecycleListener(PluginLifecycleListener listener) {
        if (listeners.remove(listener)) {
            log.debug("Removed lifecycle listener: {}", listener.getClass().getSimpleName());
        }
    }

    /**
     * 执行批量操作
     */
    private BatchOperationResult performBatchOperation(Collection<Plugin> plugins, String operation, 
                                                      PluginOperation pluginOperation) {
        int totalCount = plugins.size();
        int successCount = 0;
        List<PluginOperationError> errors = new ArrayList<>();
        
        for (Plugin plugin : plugins) {
            try {
                pluginOperation.execute(plugin);
                successCount++;
            } catch (Exception e) {
                errors.add(new PluginOperationError(
                    plugin.getMetadata().getId(),
                    operation,
                    e.getMessage(),
                    e
                ));
                log.warn("Failed to {} plugin: {}", operation, plugin.getMetadata().getId(), e);
            }
        }
        
        int failureCount = totalCount - successCount;
        log.info("Batch {} operation completed: {}/{} successful", operation, successCount, totalCount);
        
        return new BatchOperationResult(totalCount, successCount, failureCount, errors);
    }

    /**
     * 通知状态变化前事件
     */
    private boolean notifyBeforeStateChange(Plugin plugin, PluginState fromState, PluginState toState) {
        for (PluginLifecycleListener listener : listeners) {
            try {
                if (!listener.beforeStateChange(plugin, fromState, toState)) {
                    return false;
                }
            } catch (Exception e) {
                log.warn("Listener error in beforeStateChange: {}", listener.getClass().getSimpleName(), e);
            }
        }
        return true;
    }

    /**
     * 通知状态变化后事件
     */
    private void notifyAfterStateChange(Plugin plugin, PluginState fromState, PluginState toState) {
        notifyListeners(l -> l.afterStateChange(plugin, fromState, toState));
    }

    /**
     * 通知监听器
     */
    private void notifyListeners(ListenerNotification notification) {
        for (PluginLifecycleListener listener : listeners) {
            try {
                notification.notify(listener);
            } catch (Exception e) {
                log.warn("Listener error: {}", listener.getClass().getSimpleName(), e);
            }
        }
    }

    @FunctionalInterface
    private interface PluginOperation {
        void execute(Plugin plugin) throws Exception;
    }

    @FunctionalInterface
    private interface ListenerNotification {
        void notify(PluginLifecycleListener listener);
    }
}
