package com.talkweb.ai.indexer.core.loader;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginMetadata;

import java.nio.file.Path;
import java.util.Optional;

/**
 * 插件加载器接口，负责从不同来源加载插件
 */
public interface PluginLoader {

    /**
     * 从指定路径加载插件
     * 
     * @param pluginPath 插件文件路径
     * @return 加载的插件实例
     * @throws PluginLoadException 如果加载失败
     */
    Plugin loadPlugin(Path pluginPath) throws PluginLoadException;

    /**
     * 从指定路径提取插件元数据
     * 
     * @param pluginPath 插件文件路径
     * @return 插件元数据
     * @throws PluginLoadException 如果提取失败
     */
    PluginMetadata extractMetadata(Path pluginPath) throws PluginLoadException;

    /**
     * 检查指定路径是否为有效的插件文件
     * 
     * @param pluginPath 插件文件路径
     * @return 如果是有效插件文件返回true
     */
    boolean isValidPlugin(Path pluginPath);

    /**
     * 获取支持的插件文件扩展名
     * 
     * @return 支持的文件扩展名数组
     */
    String[] getSupportedExtensions();

    /**
     * 验证插件完整性
     * 
     * @param pluginPath 插件文件路径
     * @return 验证结果
     */
    PluginValidationResult validatePlugin(Path pluginPath);

    /**
     * 插件验证结果
     */
    class PluginValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final ValidationError errorType;

        public PluginValidationResult(boolean valid) {
            this(valid, null, null);
        }

        public PluginValidationResult(boolean valid, String errorMessage, ValidationError errorType) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.errorType = errorType;
        }

        public boolean isValid() {
            return valid;
        }

        public Optional<String> getErrorMessage() {
            return Optional.ofNullable(errorMessage);
        }

        public Optional<ValidationError> getErrorType() {
            return Optional.ofNullable(errorType);
        }

        public enum ValidationError {
            MISSING_DESCRIPTOR,
            INVALID_DESCRIPTOR,
            MISSING_MAIN_CLASS,
            INVALID_SIGNATURE,
            CORRUPTED_FILE,
            UNSUPPORTED_VERSION
        }
    }
}
