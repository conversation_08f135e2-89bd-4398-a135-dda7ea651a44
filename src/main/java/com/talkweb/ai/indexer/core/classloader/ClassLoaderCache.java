package com.talkweb.ai.indexer.core.classloader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.ref.WeakReference;
import java.net.URLClassLoader;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 类加载器缓存
 * 提供高效的类加载器缓存机制，支持自动清理和内存管理
 */
public class ClassLoaderCache {
    
    private static final Logger log = LoggerFactory.getLogger(ClassLoaderCache.class);
    
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor;
    private final long maxCacheSize;
    private final long maxIdleTime;
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong evictions = new AtomicLong(0);
    
    // 默认配置
    private static final long DEFAULT_MAX_CACHE_SIZE = 100;
    private static final long DEFAULT_MAX_IDLE_TIME = 30 * 60 * 1000; // 30分钟
    private static final long DEFAULT_CLEANUP_INTERVAL = 5 * 60 * 1000; // 5分钟

    public ClassLoaderCache() {
        this(DEFAULT_MAX_CACHE_SIZE, DEFAULT_MAX_IDLE_TIME, DEFAULT_CLEANUP_INTERVAL);
    }

    public ClassLoaderCache(long maxCacheSize, long maxIdleTime, long cleanupInterval) {
        this.maxCacheSize = maxCacheSize;
        this.maxIdleTime = maxIdleTime;
        
        // 启动清理任务
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ClassLoaderCache-Cleanup");
            t.setDaemon(true);
            return t;
        });
        
        this.cleanupExecutor.scheduleWithFixedDelay(
            this::cleanup, 
            cleanupInterval, 
            cleanupInterval, 
            TimeUnit.MILLISECONDS
        );
        
        log.info("ClassLoaderCache initialized with maxSize={}, maxIdleTime={}ms, cleanupInterval={}ms", 
                maxCacheSize, maxIdleTime, cleanupInterval);
    }

    /**
     * 获取类加载器
     */
    public URLClassLoader get(String key) {
        CacheEntry entry = cache.get(key);
        if (entry != null) {
            URLClassLoader classLoader = entry.getClassLoader();
            if (classLoader != null) {
                entry.updateAccessTime();
                cacheHits.incrementAndGet();
                log.debug("Cache hit for key: {}", key);
                return classLoader;
            } else {
                // 类加载器已被垃圾回收
                cache.remove(key);
                log.debug("ClassLoader was garbage collected for key: {}", key);
            }
        }
        
        cacheMisses.incrementAndGet();
        log.debug("Cache miss for key: {}", key);
        return null;
    }

    /**
     * 缓存类加载器
     */
    public void put(String key, URLClassLoader classLoader) {
        if (key == null || classLoader == null) {
            return;
        }
        
        // 检查缓存大小限制
        if (cache.size() >= maxCacheSize) {
            evictLeastRecentlyUsed();
        }
        
        CacheEntry entry = new CacheEntry(classLoader);
        cache.put(key, entry);
        
        log.debug("Cached class loader for key: {}", key);
    }

    /**
     * 移除缓存项
     */
    public URLClassLoader remove(String key) {
        CacheEntry entry = cache.remove(key);
        if (entry != null) {
            URLClassLoader classLoader = entry.getClassLoader();
            log.debug("Removed class loader from cache for key: {}", key);
            return classLoader;
        }
        return null;
    }

    /**
     * 检查是否包含指定的键
     */
    public boolean containsKey(String key) {
        CacheEntry entry = cache.get(key);
        if (entry != null) {
            URLClassLoader classLoader = entry.getClassLoader();
            if (classLoader != null) {
                return true;
            } else {
                // 类加载器已被垃圾回收
                cache.remove(key);
            }
        }
        return false;
    }

    /**
     * 获取缓存大小
     */
    public int size() {
        return cache.size();
    }

    /**
     * 清空缓存
     */
    public void clear() {
        cache.clear();
        log.info("ClassLoader cache cleared");
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getStatistics() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        double hitRate = total > 0 ? (double) hits / total : 0.0;
        
        return new CacheStatistics(
            cache.size(),
            hits,
            misses,
            hitRate,
            evictions.get()
        );
    }

    /**
     * 清理过期的缓存项
     */
    private void cleanup() {
        long currentTime = System.currentTimeMillis();
        int removedCount = 0;
        
        cache.entrySet().removeIf(entry -> {
            CacheEntry cacheEntry = entry.getValue();
            
            // 检查类加载器是否已被垃圾回收
            if (cacheEntry.getClassLoader() == null) {
                return true;
            }
            
            // 检查是否超过最大空闲时间
            if (currentTime - cacheEntry.getLastAccessTime() > maxIdleTime) {
                return true;
            }
            
            return false;
        });
        
        if (removedCount > 0) {
            log.debug("Cleaned up {} expired cache entries", removedCount);
        }
    }

    /**
     * 驱逐最近最少使用的缓存项
     */
    private void evictLeastRecentlyUsed() {
        String lruKey = null;
        long oldestAccessTime = Long.MAX_VALUE;
        
        for (Map.Entry<String, CacheEntry> entry : cache.entrySet()) {
            CacheEntry cacheEntry = entry.getValue();
            if (cacheEntry.getLastAccessTime() < oldestAccessTime) {
                oldestAccessTime = cacheEntry.getLastAccessTime();
                lruKey = entry.getKey();
            }
        }
        
        if (lruKey != null) {
            cache.remove(lruKey);
            evictions.incrementAndGet();
            log.debug("Evicted LRU cache entry for key: {}", lruKey);
        }
    }

    /**
     * 关闭缓存
     */
    public void shutdown() {
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        clear();
        log.info("ClassLoader cache shutdown completed");
    }

    /**
     * 缓存项
     */
    private static class CacheEntry {
        private final WeakReference<URLClassLoader> classLoaderRef;
        private volatile long lastAccessTime;

        public CacheEntry(URLClassLoader classLoader) {
            this.classLoaderRef = new WeakReference<>(classLoader);
            this.lastAccessTime = System.currentTimeMillis();
        }

        public URLClassLoader getClassLoader() {
            return classLoaderRef.get();
        }

        public long getLastAccessTime() {
            return lastAccessTime;
        }

        public void updateAccessTime() {
            this.lastAccessTime = System.currentTimeMillis();
        }
    }

    /**
     * 缓存统计信息
     */
    public static class CacheStatistics {
        private final int size;
        private final long hits;
        private final long misses;
        private final double hitRate;
        private final long evictions;

        public CacheStatistics(int size, long hits, long misses, double hitRate, long evictions) {
            this.size = size;
            this.hits = hits;
            this.misses = misses;
            this.hitRate = hitRate;
            this.evictions = evictions;
        }

        public int getSize() { return size; }
        public long getHits() { return hits; }
        public long getMisses() { return misses; }
        public double getHitRate() { return hitRate; }
        public long getEvictions() { return evictions; }

        @Override
        public String toString() {
            return String.format("CacheStatistics{size=%d, hits=%d, misses=%d, hitRate=%.2f%%, evictions=%d}",
                               size, hits, misses, hitRate * 100, evictions);
        }
    }
}
