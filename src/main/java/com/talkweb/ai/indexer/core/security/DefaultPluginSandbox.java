package com.talkweb.ai.indexer.core.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.Permission;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 默认插件沙箱实现
 */
public class DefaultPluginSandbox implements PluginSandbox {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultPluginSandbox.class);
    
    private final String sandboxId;
    private final String pluginId;
    private final Path workingDirectory;
    private final Path tempDirectory;
    private final Set<Permission> allowedPermissions;
    private final AtomicBoolean destroyed = new AtomicBoolean(false);
    
    private URLClassLoader classLoader;
    private ResourceLimits resourceLimits;
    private long creationTime;

    public DefaultPluginSandbox(String pluginId) throws IOException {
        this.sandboxId = "sandbox_" + pluginId + "_" + System.currentTimeMillis();
        this.pluginId = pluginId;
        this.allowedPermissions = new HashSet<>();
        this.creationTime = System.currentTimeMillis();
        
        // 创建沙箱目录
        Path sandboxRoot = Paths.get(System.getProperty("java.io.tmpdir"), "plugin-sandbox", sandboxId);
        this.workingDirectory = sandboxRoot.resolve("work");
        this.tempDirectory = sandboxRoot.resolve("temp");
        
        Files.createDirectories(workingDirectory);
        Files.createDirectories(tempDirectory);
        
        // 设置默认资源限制
        this.resourceLimits = new ResourceLimits(
            100 * 1024 * 1024, // 100MB 内存
            500 * 1024 * 1024, // 500MB 磁盘
            10,                // 10个线程
            30 * 1000,         // 30秒执行时间
            50                 // 50个文件句柄
        );
        
        log.info("Created sandbox for plugin: {} (ID: {})", pluginId, sandboxId);
    }

    @Override
    public String getSandboxId() {
        return sandboxId;
    }

    @Override
    public String getPluginId() {
        return pluginId;
    }

    @Override
    public URLClassLoader getClassLoader() {
        if (destroyed.get()) {
            throw new IllegalStateException("Sandbox has been destroyed");
        }
        return classLoader;
    }

    @Override
    public Path getWorkingDirectory() {
        return workingDirectory;
    }

    @Override
    public Path getTempDirectory() {
        return tempDirectory;
    }

    @Override
    public boolean checkFileAccess(File file, FileAccess access) {
        if (destroyed.get()) {
            return false;
        }
        
        Path filePath = file.toPath().toAbsolutePath();
        Path workDir = workingDirectory.toAbsolutePath();
        Path tempDir = tempDirectory.toAbsolutePath();
        
        // 只允许访问沙箱目录内的文件
        boolean inWorkDir = filePath.startsWith(workDir);
        boolean inTempDir = filePath.startsWith(tempDir);
        
        if (!inWorkDir && !inTempDir) {
            log.warn("File access denied outside sandbox: {} (Plugin: {})", filePath, pluginId);
            return false;
        }
        
        // 根据访问类型进行额外检查
        switch (access) {
            case READ:
                return Files.isReadable(filePath);
            case WRITE:
                return Files.isWritable(filePath.getParent());
            case EXECUTE:
                return Files.isExecutable(filePath);
            case DELETE:
                return Files.isWritable(filePath.getParent()) && Files.exists(filePath);
            default:
                return false;
        }
    }

    @Override
    public boolean checkNetworkAccess(String host, int port, String protocol) {
        if (destroyed.get()) {
            return false;
        }

        // 允许访问本地主机的某些端口（用于开发和测试）
        if ("localhost".equals(host) || "127.0.0.1".equals(host)) {
            // 允许访问常见的开发端口
            if (port >= 8000 && port <= 9999) {
                return true;
            }
        }

        // 允许访问HTTPS端口（443）进行安全通信
        if (port == 443 && "https".equals(protocol)) {
            return true;
        }

        // 其他情况默认拒绝
        log.warn("Network access denied: {}://{}:{} (Plugin: {})", protocol, host, port, pluginId);
        return false;
    }

    @Override
    public boolean checkSystemPropertyAccess(String property, PropertyAccess access) {
        if (destroyed.get()) {
            return false;
        }
        
        // 允许读取一些安全的系统属性
        if (access == PropertyAccess.READ) {
            return isAllowedSystemProperty(property);
        }
        
        // 禁止写入系统属性
        log.warn("System property write access denied: {} (Plugin: {})", property, pluginId);
        return false;
    }

    @Override
    public Set<Permission> getAllowedPermissions() {
        return new HashSet<>(allowedPermissions);
    }

    @Override
    public void addAllowedPermission(Permission permission) {
        if (!destroyed.get()) {
            allowedPermissions.add(permission);
            log.debug("Added permission to sandbox: {} (Plugin: {})", permission.getName(), pluginId);
        }
    }

    @Override
    public void removeAllowedPermission(Permission permission) {
        if (!destroyed.get()) {
            allowedPermissions.remove(permission);
            log.debug("Removed permission from sandbox: {} (Plugin: {})", permission.getName(), pluginId);
        }
    }

    @Override
    public ResourceLimits getResourceLimits() {
        return resourceLimits;
    }

    @Override
    public void setResourceLimits(ResourceLimits limits) {
        if (!destroyed.get()) {
            this.resourceLimits = limits;
            log.debug("Updated resource limits for sandbox: {} (Plugin: {})", sandboxId, pluginId);
        }
    }

    @Override
    public ResourceUsage getCurrentResourceUsage() {
        if (destroyed.get()) {
            return new ResourceUsage(0, 0, 0, 0, 0);
        }
        
        // 简化实现：实际应该收集真实的资源使用情况
        long usedMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        long usedDisk = calculateDiskUsage();
        int activeThreads = Thread.activeCount();
        long executionTime = System.currentTimeMillis() - creationTime;
        int openFileHandles = 0; // 简化实现
        
        return new ResourceUsage(usedMemory, usedDisk, activeThreads, executionTime, openFileHandles);
    }

    @Override
    public boolean isResourceLimitExceeded() {
        if (destroyed.get()) {
            return false;
        }
        
        ResourceUsage usage = getCurrentResourceUsage();
        
        return usage.getUsedMemoryBytes() > resourceLimits.getMaxMemoryBytes() ||
               usage.getUsedDiskBytes() > resourceLimits.getMaxDiskBytes() ||
               usage.getActiveThreads() > resourceLimits.getMaxThreads() ||
               usage.getExecutionTimeMs() > resourceLimits.getMaxExecutionTimeMs() ||
               usage.getOpenFileHandles() > resourceLimits.getMaxFileHandles();
    }

    @Override
    public void destroy() {
        if (destroyed.compareAndSet(false, true)) {
            try {
                // 关闭类加载器
                if (classLoader != null) {
                    classLoader.close();
                }
                
                // 清理沙箱目录
                cleanupSandboxDirectories();
                
                log.info("Destroyed sandbox: {} (Plugin: {})", sandboxId, pluginId);
                
            } catch (Exception e) {
                log.error("Error destroying sandbox: {} (Plugin: {})", sandboxId, pluginId, e);
            }
        }
    }

    @Override
    public boolean isDestroyed() {
        return destroyed.get();
    }

    /**
     * 检查是否为允许的系统属性
     */
    private boolean isAllowedSystemProperty(String property) {
        // 允许读取的安全系统属性列表
        String[] allowedProperties = {
            "java.version",
            "java.vendor",
            "java.vm.name",
            "java.vm.version",
            "os.name",
            "os.arch",
            "file.separator",
            "path.separator",
            "line.separator"
        };
        
        for (String allowed : allowedProperties) {
            if (property.equals(allowed)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 计算磁盘使用量
     */
    private long calculateDiskUsage() {
        try {
            return Files.walk(workingDirectory)
                .filter(Files::isRegularFile)
                .mapToLong(path -> {
                    try {
                        return Files.size(path);
                    } catch (IOException e) {
                        return 0;
                    }
                })
                .sum();
        } catch (IOException e) {
            log.warn("Failed to calculate disk usage for sandbox: {}", sandboxId, e);
            return 0;
        }
    }

    /**
     * 清理沙箱目录
     */
    private void cleanupSandboxDirectories() {
        try {
            if (Files.exists(workingDirectory)) {
                Files.walk(workingDirectory)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                    .forEach(path -> {
                        try {
                            Files.deleteIfExists(path);
                        } catch (IOException e) {
                            log.warn("Failed to delete sandbox file: {}", path, e);
                        }
                    });
            }
            
            if (Files.exists(tempDirectory)) {
                Files.walk(tempDirectory)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.deleteIfExists(path);
                        } catch (IOException e) {
                            log.warn("Failed to delete sandbox temp file: {}", path, e);
                        }
                    });
            }
        } catch (IOException e) {
            log.error("Failed to cleanup sandbox directories: {}", sandboxId, e);
        }
    }

    /**
     * 设置类加载器
     */
    public void setClassLoader(URLClassLoader classLoader) {
        this.classLoader = classLoader;
    }
}
