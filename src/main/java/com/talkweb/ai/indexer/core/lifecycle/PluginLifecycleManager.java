package com.talkweb.ai.indexer.core.lifecycle;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginContext;
import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginState;

import java.util.Collection;
import java.util.List;

/**
 * 插件生命周期管理器接口
 * 负责管理插件的初始化、启动、停止、销毁等生命周期操作
 */
public interface PluginLifecycleManager {

    /**
     * 初始化插件
     * 
     * @param plugin 要初始化的插件
     * @param context 插件上下文
     * @throws PluginInitException 如果初始化失败
     */
    void initializePlugin(Plugin plugin, PluginContext context) throws PluginInitException;

    /**
     * 启动插件
     * 
     * @param plugin 要启动的插件
     * @throws PluginStartException 如果启动失败
     */
    void startPlugin(Plugin plugin) throws PluginStartException;

    /**
     * 停止插件
     * 
     * @param plugin 要停止的插件
     * @throws PluginStopException 如果停止失败
     */
    void stopPlugin(Plugin plugin) throws PluginStopException;

    /**
     * 销毁插件
     * 
     * @param plugin 要销毁的插件
     * @throws PluginDestroyException 如果销毁失败
     */
    void destroyPlugin(Plugin plugin) throws PluginDestroyException;

    /**
     * 批量初始化插件
     * 
     * @param plugins 要初始化的插件集合
     * @param context 插件上下文
     * @return 初始化结果
     */
    BatchOperationResult initializePlugins(Collection<Plugin> plugins, PluginContext context);

    /**
     * 批量启动插件
     * 
     * @param plugins 要启动的插件集合
     * @return 启动结果
     */
    BatchOperationResult startPlugins(Collection<Plugin> plugins);

    /**
     * 批量停止插件
     * 
     * @param plugins 要停止的插件集合
     * @return 停止结果
     */
    BatchOperationResult stopPlugins(Collection<Plugin> plugins);

    /**
     * 批量销毁插件
     * 
     * @param plugins 要销毁的插件集合
     * @return 销毁结果
     */
    BatchOperationResult destroyPlugins(Collection<Plugin> plugins);

    /**
     * 检查插件状态转换是否有效
     * 
     * @param currentState 当前状态
     * @param targetState 目标状态
     * @return 如果转换有效返回true
     */
    boolean isValidStateTransition(PluginState currentState, PluginState targetState);

    /**
     * 获取插件的下一个有效状态
     * 
     * @param currentState 当前状态
     * @return 可能的下一个状态列表
     */
    List<PluginState> getNextValidStates(PluginState currentState);

    /**
     * 添加生命周期监听器
     * 
     * @param listener 生命周期监听器
     */
    void addLifecycleListener(PluginLifecycleListener listener);

    /**
     * 移除生命周期监听器
     * 
     * @param listener 生命周期监听器
     */
    void removeLifecycleListener(PluginLifecycleListener listener);

    /**
     * 批量操作结果
     */
    class BatchOperationResult {
        private final int totalCount;
        private final int successCount;
        private final int failureCount;
        private final List<PluginOperationError> errors;

        public BatchOperationResult(int totalCount, int successCount, int failureCount, 
                                   List<PluginOperationError> errors) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.errors = errors;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public List<PluginOperationError> getErrors() {
            return errors;
        }

        public boolean isAllSuccessful() {
            return failureCount == 0;
        }

        public boolean hasErrors() {
            return failureCount > 0;
        }
    }

    /**
     * 插件操作错误信息
     */
    class PluginOperationError {
        private final String pluginId;
        private final String operation;
        private final String errorMessage;
        private final Throwable cause;

        public PluginOperationError(String pluginId, String operation, String errorMessage, Throwable cause) {
            this.pluginId = pluginId;
            this.operation = operation;
            this.errorMessage = errorMessage;
            this.cause = cause;
        }

        public String getPluginId() {
            return pluginId;
        }

        public String getOperation() {
            return operation;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public Throwable getCause() {
            return cause;
        }
    }
}
