package com.talkweb.ai.indexer.core.converter;

import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.util.ConversionCacheManager;
import com.talkweb.ai.indexer.core.util.ConversionErrorHandler;
import com.talkweb.ai.indexer.core.util.FileFormatDetector;

import java.io.File;
import java.util.Objects;
import java.util.Set;

/**
 * 文档转换器抽象基类，实现了DocumentConverter接口的通用功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public abstract class AbstractDocumentConverter implements DocumentConverter {
    
    protected final ConversionCacheManager.Cache<String, ConversionResult> cache;
    protected final ConversionErrorHandler errorHandler;
    protected final ConversionMetadata metadata;
    
    /**
     * 创建文档转换器
     */
    protected AbstractDocumentConverter() {
        this.metadata = createMetadata();
        this.cache = createCache();
        this.errorHandler = createErrorHandler();
    }
    
    /**
     * 创建转换器元数据
     * 
     * @return 转换器元数据
     */
    protected abstract ConversionMetadata createMetadata();
    
    /**
     * 创建缓存
     *
     * @return 缓存实例
     */
    protected ConversionCacheManager.Cache<String, ConversionResult> createCache() {
        String cacheName = getClass().getSimpleName() + "Cache";

        // Dynamic cache sizing based on available memory
        Runtime runtime = Runtime.getRuntime();
        long availableMemory = runtime.maxMemory() - (runtime.totalMemory() - runtime.freeMemory());
        int dynamicCacheSize = (int) Math.min(1000, Math.max(100, availableMemory / (10 * 1024 * 1024))); // 1 entry per 10MB

        ConversionCacheManager.CacheConfig config = ConversionCacheManager.CacheConfig.builder()
                .type(ConversionCacheManager.CacheType.LRU)
                .maxSize(dynamicCacheSize)
                .expireAfterWrite(30, java.util.concurrent.TimeUnit.MINUTES)
                .build();

        return ConversionCacheManager.getCache(cacheName, config);
    }
    
    /**
     * 创建错误处理器
     * 
     * @return 错误处理器
     */
    protected ConversionErrorHandler createErrorHandler() {
        return ConversionErrorHandler.builder().build();
    }
    
    @Override
    public ConversionMetadata getMetadata() {
        return metadata;
    }
    
    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder().build();
    }
    
    @Override
    public final ConversionResult convert(File input, ConversionContext context) throws ConversionException {
        try {
            // 验证输入
            validateInput(input, context);

            // 检查缓存
            String cacheKey = getCacheKey(input, context);
            ConversionResult cachedResult = cache.get(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }

            // 执行转换
            ConversionResult result = doConvert(input, context);

            // 缓存结果
            if (result != null && result.isSuccess()) {
                cache.put(cacheKey, result);
            }

            return result;
        } catch (ConversionException e) {
            // Re-throw ConversionException to maintain expected behavior for tests
            throw e;
        } catch (Exception e) {
            return ConversionErrorHandler.handleError(e, context);
        }
    }
    
    @Override
    public boolean supports(File input, ConversionContext context) {
        if (input == null || !input.exists() || !input.isFile()) {
            return false;
        }
        
        String extension = FileFormatDetector.getFileExtension(input);
        return extension != null && supportsExtension(extension);
    }
    
    /**
     * 执行实际的转换操作
     * 
     * @param input 输入文件
     * @param context 转换上下文
     * @return 转换结果
     * @throws ConversionException 转换异常
     */
    protected abstract ConversionResult doConvert(File input, ConversionContext context) throws ConversionException;
    
    /**
     * 验证输入
     * 
     * @param input 输入文件
     * @param context 转换上下文
     * @throws ConversionException 如果验证失败
     */
    protected void validateInput(File input, ConversionContext context) throws ConversionException {
        if (input == null) {
            throw new ConversionException("输入文件不能为空");
        }
        
        if (!input.exists()) {
            throw new ConversionException("输入文件不存在: " + input.getAbsolutePath());
        }
        
        if (!input.isFile()) {
            throw new ConversionException("输入路径不是文件: " + input.getAbsolutePath());
        }
        
        if (!input.canRead()) {
            throw new ConversionException("无法读取输入文件: " + input.getAbsolutePath());
        }
        
        String extension = FileFormatDetector.getFileExtension(input);
        if (extension == null || !supportsExtension(extension)) {
            throw new ConversionException("不支持的文件类型: " + extension);
        }
        
        if (context == null) {
            throw new ConversionException("转换上下文不能为空");
        }
    }
    
    /**
     * 获取缓存键
     * 
     * @param input 输入文件
     * @param context 转换上下文
     * @return 缓存键
     */
    protected String getCacheKey(File input, ConversionContext context) {
        return input.getAbsolutePath() + "|" + context.getMode() + "|" + 
                Objects.hash(context.getOptions().getAllOptions());
    }
    
    @Override
    public void destroy() {
        // 清理资源
    }
}