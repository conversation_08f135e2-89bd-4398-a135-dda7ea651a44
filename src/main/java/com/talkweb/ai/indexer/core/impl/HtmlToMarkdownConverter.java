
package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.converter.AbstractDocumentConverter;
import com.talkweb.ai.indexer.core.converter.ConversionCapabilities;
import com.talkweb.ai.indexer.core.converter.ConversionContext;
import com.talkweb.ai.indexer.core.converter.ConversionMetadata;
import com.talkweb.ai.indexer.util.HtmlConversionMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Set;

/**
 * HTML to Markdown converter implementation using the new architecture
 *
 * This converter provides enhanced HTML to Markdown conversion with
 * improved structure preservation and configurable processing modes.
 *
 * Features:
 * - Support for HTML and HTM files
 * - Enhanced table processing with CSS framework compatibility
 * - Configurable conversion modes (strict/loose)
 * - Optimized processing for large files
 * - Comprehensive element support
 *
 * <AUTHOR> Assistant
 * @version 3.0 (Refactored to use new architecture)
 */
public class HtmlToMarkdownConverter extends AbstractDocumentConverter {

    private static final Logger logger = LoggerFactory.getLogger(HtmlToMarkdownConverter.class);

    /**
     * Default constructor
     */
    public HtmlToMarkdownConverter() {
        super();
    }

    @Override
    protected ConversionMetadata createMetadata() {
        return ConversionMetadata.builder("HTML to Markdown Converter")
                .description("Converts HTML files (.html, .htm) to Markdown format with enhanced structure preservation")
                .version("3.0")
                .attribute("author", "AI Assistant")
                .attribute("supportedInputFormats", Set.of("html", "htm"))
                .attribute("supportedOutputFormats", Set.of("md"))
                .build();
    }

    @Override
    public Set<String> getSupportedExtensions() {
        return Set.of("html", "htm");
    }

    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.HEADINGS)
                .feature(ConversionCapabilities.Features.TABLES)
                .feature(ConversionCapabilities.Features.LISTS)
                .feature(ConversionCapabilities.Features.IMAGES)
                .feature(ConversionCapabilities.Features.LINKS)
                .capability("strictMode", true)
                .capability("looseMode", true)
                .capability("largeFileOptimization", true)
                .capability("cssFrameworkSupport", true)
                .capability("maxFileSize", 50 * 1024 * 1024) // 50MB
                .build();
    }

    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) throws ConversionException {
        logger.debug("Processing HTML file: {}", inputFile.getPath());

        // Generate output file path
        String inputFileName = inputFile.getName();
        String outputFileName = getOutputFileName(inputFileName);
        Path outputPath = Path.of(inputFile.getParent(), outputFileName);

        try {
            // Read HTML content
            String htmlContent = java.nio.file.Files.readString(inputFile.toPath());
            logger.debug("Read HTML content from file: {} (size: {} characters)", inputFile.getPath(), htmlContent.length());

            // Get conversion mode from context
            HtmlConversionMode mode = getConversionMode(context);

            // Convert to Markdown using optimized method for large files
            String markdown;
            if (htmlContent.length() > 100_000) {
                logger.debug("Using optimized conversion for large HTML file (size: {} characters)", htmlContent.length());
                markdown = com.talkweb.ai.indexer.util.HtmlToMarkdownConverter.convertLarge(htmlContent, mode);
            } else {
                markdown = com.talkweb.ai.indexer.util.HtmlToMarkdownConverter.convert(htmlContent, mode);
            }
            logger.debug("Converted HTML to Markdown (size: {} characters)", markdown.length());

            // Validate conversion result
            if (markdown == null || markdown.trim().isEmpty()) {
                logger.warn("Conversion resulted in empty content for file: {}", inputFile.getPath());
                if (mode == HtmlConversionMode.STRICT) {
                    throw new ConversionException("Conversion resulted in empty content in STRICT mode");
                }
                // In LOOSE mode, use a minimal markdown content
                markdown = "<!-- Empty content after HTML to Markdown conversion -->\n";
            }

            return new ConversionResult(
                ConversionResult.Status.SUCCESS,
                inputFile.getPath(),
                outputPath.toString(),
                markdown.trim()
            );

        } catch (IOException e) {
            logger.error("Failed to read HTML file: {}", inputFile.getPath(), e);
            throw new ConversionException("Failed to read HTML file: " + inputFile.getPath(), e);
        } catch (Exception e) {
            logger.error("HTML to Markdown conversion failed for file: {}", inputFile.getPath(), e);
            HtmlConversionMode mode = getConversionMode(context);
            if (mode == HtmlConversionMode.STRICT) {
                throw new ConversionException("HTML to Markdown conversion failed in STRICT mode for file: " + inputFile.getPath(), e);
            }
            throw new ConversionException("HTML to Markdown conversion failed for file: " + inputFile.getPath(), e);
        }
    }

    /**
     * Gets conversion mode from context
     */
    private HtmlConversionMode getConversionMode(ConversionContext context) {
        String modeStr = context.getOptions().getStringOption("conversionMode", "LOOSE");

        try {
            return HtmlConversionMode.valueOf(modeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid conversion mode: " + modeStr + ", using LOOSE mode");
            return HtmlConversionMode.LOOSE;
        }
    }

    /**
     * Generates output file name for the converted Markdown file
     */
    private String getOutputFileName(String inputFileName) {
        if (inputFileName == null || inputFileName.isEmpty()) {
            return "converted.md";
        }

        // Remove HTML extension and add .md
        String baseName = inputFileName;
        if (baseName.toLowerCase().endsWith(".html")) {
            baseName = baseName.substring(0, baseName.length() - 5);
        } else if (baseName.toLowerCase().endsWith(".htm")) {
            baseName = baseName.substring(0, baseName.length() - 4);
        }

        return baseName + ".md";
    }


}
