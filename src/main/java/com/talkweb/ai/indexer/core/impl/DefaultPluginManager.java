package com.talkweb.ai.indexer.core.impl;

import java.nio.file.*;
import java.io.IOException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

import com.talkweb.ai.indexer.core.*;
import com.talkweb.ai.indexer.core.classloader.DefaultPluginClassLoaderManager;
import com.talkweb.ai.indexer.core.classloader.PluginClassLoaderManager;
import com.talkweb.ai.indexer.core.lifecycle.DefaultPluginLifecycleManager;
import com.talkweb.ai.indexer.core.lifecycle.PluginLifecycleManager;
import com.talkweb.ai.indexer.core.loader.JarPluginLoader;
import com.talkweb.ai.indexer.core.loader.PluginLoader;
import com.talkweb.ai.indexer.core.loader.PluginLoadException;
import com.talkweb.ai.indexer.core.registry.DefaultPluginRegistry;
import com.talkweb.ai.indexer.core.registry.PluginRegistry;
import com.talkweb.ai.indexer.core.registry.PluginRegistryException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DefaultPluginManager implements PluginManager {
    private static final Logger log = LoggerFactory.getLogger(DefaultPluginManager.class);

    // 组件依赖
    private final PluginConfig config;
    private final PluginLoader pluginLoader;
    private final PluginLifecycleManager lifecycleManager;
    private final PluginClassLoaderManager classLoaderManager;
    private final PluginRegistry pluginRegistry;

    // 热重载相关
    private long debounceTimeMs = 500;
    private long pollInterval;
    private WatchService watchService;
    private Thread watchThread;
    private boolean hotReloadEnabled;
    private final Map<String, Long> reloadTimings = new ConcurrentHashMap<>();


    @Override
    public void installPlugin(Path pluginPath, boolean force) throws PluginException {
        try {
            if (!Files.exists(pluginPath)) {
                throw new PluginException("Plugin file not found: " + pluginPath);
            }

            // 提取插件元数据
            PluginMetadata metadata = pluginLoader.extractMetadata(pluginPath);
            String pluginId = metadata.getId();

            // 检查是否已安装
            if (pluginRegistry.isPluginRegistered(pluginId) && !force) {
                log.warn("Plugin {} already installed", pluginId);
                return;
            }

            // 如果强制安装，先卸载现有插件
            if (force && pluginRegistry.isPluginRegistered(pluginId)) {
                uninstallPlugin(pluginId, true);
            }

            // 加载插件
            Plugin plugin = pluginLoader.loadPlugin(pluginPath);

            // 注册插件
            pluginRegistry.registerPlugin(plugin);

            log.info("Successfully installed plugin: {} v{}", pluginId, metadata.getVersion());

        } catch (PluginLoadException | PluginRegistryException e) {
            throw new PluginException("Failed to install plugin from " + pluginPath, e);
        } catch (Exception e) {
            throw new PluginException("Failed to install plugin from " + pluginPath, e);
        }
    }



    @Override
    public void start() throws PluginException {
        loadPlugins();
        initPlugins();
        startPlugins();
    }

    @Override
    public void shutdown() {
        try {
            stopPlugins();
            destroyPlugins();
            disableHotReload();
        } catch (PluginException e) {
            log.error("Error during shutdown", e);
        }
    }

    @Override
    public void registerPlugin(Plugin plugin) throws PluginException {
        try {
            pluginRegistry.registerPlugin(plugin);
        } catch (PluginRegistryException e) {
            throw new PluginException("Failed to register plugin: " + plugin.getMetadata().getId(), e);
        }
    }

    @Override
    public Optional<Plugin> getPlugin(String pluginId, String version) {
        return pluginRegistry.getPlugin(pluginId, version);
    }

    @Override
    public boolean unregisterPlugin(String pluginId) throws PluginException {
        return uninstallPlugin(pluginId, false);
    }

    @Override
    public void stopPlugins() throws PluginException {
        try {
            lifecycleManager.stopPlugins(pluginRegistry.getAllPlugins());
        } catch (Exception e) {
            throw new PluginException("Failed to stop plugins", e);
        }
    }

    @Override
    public void destroyPlugins() throws PluginException {
        try {
            lifecycleManager.destroyPlugins(pluginRegistry.getAllPlugins());
            pluginRegistry.clear();
            classLoaderManager.clearAllClassLoaders();
        } catch (Exception e) {
            throw new PluginException("Failed to destroy plugins", e);
        }
    }

    @Override
    public void loadPlugins() throws PluginException {
        Path pluginsDir = config.getPluginsDir();
        if (!Files.exists(pluginsDir)) {
            log.warn("Plugins directory does not exist: {}", pluginsDir);
            return;
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(pluginsDir, "*.jar")) {
            for (Path pluginPath : stream) {
                try {
                    installPlugin(pluginPath, false);
                } catch (PluginException e) {
                    log.error("Failed to load plugin from {}", pluginPath, e);
                }
            }
        } catch (IOException e) {
            throw new PluginException("Failed to scan plugins directory", e);
        }
    }

    @Override
    public void initPlugins() throws PluginException {
        try {
            PluginContext context = new DefaultPluginContext(
                this,
                getClass().getClassLoader(),
                new Properties(),
                config.getPluginsDir(),
                Path.of(System.getProperty("java.io.tmpdir")),
                log
            );
            lifecycleManager.initializePlugins(pluginRegistry.getAllPlugins(), context);
        } catch (Exception e) {
            throw new PluginException("Failed to initialize plugins", e);
        }
    }

    @Override
    public void startPlugins() throws PluginException {
        try {
            lifecycleManager.startPlugins(pluginRegistry.getAllPlugins());
        } catch (Exception e) {
            throw new PluginException("Failed to start plugins", e);
        }
    }

    @Override
    public Optional<Plugin> getPlugin(String pluginId) {
        return pluginRegistry.getPlugin(pluginId);
    }

    @Override
    public Collection<Plugin> getPlugins() {
        return pluginRegistry.getAllPlugins();
    }

    @Override
    public PluginState getPluginState(String pluginId) {
        return getPlugin(pluginId)
            .map(Plugin::getState)
            .orElse(null);
    }

    public boolean reloadPlugin(String pluginId) throws PluginException {
        if (!pluginRegistry.isPluginRegistered(pluginId)) {
            return false;
        }

        Path pluginPath = config.getPluginsDir().resolve(pluginId + ".jar");

        try {
            // 先卸载插件
            uninstallPlugin(pluginId, true);
            // 重新安装插件
            installPlugin(pluginPath, true);
            return true;
        } catch (Exception e) {
            throw new PluginException("Failed to reload plugin: " + pluginId, e);
        }
    }

    public DefaultPluginManager(PluginConfig config) {
        this.config = config;
        this.pluginLoader = new JarPluginLoader();
        this.lifecycleManager = new DefaultPluginLifecycleManager();
        this.classLoaderManager = new DefaultPluginClassLoaderManager();
        this.pluginRegistry = new DefaultPluginRegistry();
    }

    // 构造函数用于依赖注入（测试用）
    public DefaultPluginManager(PluginConfig config,
                               PluginLoader pluginLoader,
                               PluginLifecycleManager lifecycleManager,
                               PluginClassLoaderManager classLoaderManager,
                               PluginRegistry pluginRegistry) {
        this.config = config;
        this.pluginLoader = pluginLoader;
        this.lifecycleManager = lifecycleManager;
        this.classLoaderManager = classLoaderManager;
        this.pluginRegistry = pluginRegistry;
    }

    @Override
    public boolean isPluginLoaded(String pluginId) {
        return getPlugin(pluginId).isPresent();
    }



    @Override
    public void reloadPlugins() {
        unloadAllPlugins();
        try {
            loadPlugins();
        } catch (PluginException e) {
            log.error("Failed to load plugins during reload", e);
        }
    }

    private void unloadAllPlugins() {
        try {
            lifecycleManager.destroyPlugins(pluginRegistry.getAllPlugins());
            pluginRegistry.clear();
            classLoaderManager.clearAllClassLoaders();
        } catch (Exception e) {
            log.error("Failed to unload all plugins", e);
        }
    }

    public Map<String, Long> getReloadTimings() {
        return new HashMap<>(reloadTimings);
    }

    @Override
    public boolean isHotReloadEnabled() {
        return hotReloadEnabled;
    }

    @Override
    public boolean uninstallPlugin(String pluginId, boolean force) throws PluginException {
        Optional<Plugin> pluginOpt = getPlugin(pluginId);
        if (!pluginOpt.isPresent()) {
            return false;
        }

        Plugin plugin = pluginOpt.get();
        if (plugin.getState() == PluginState.RUNNING && !force) {
            throw new PluginException("Cannot uninstall running plugin without force flag");
        }

        try {
            // 销毁插件
            lifecycleManager.destroyPlugin(plugin);

            // 从注册表中移除
            boolean removed = pluginRegistry.unregisterPlugin(pluginId);

            // 清理类加载器
            classLoaderManager.removeClassLoader(pluginId);

            return removed;
        } catch (Exception e) {
            throw new PluginException("Failed to uninstall plugin: " + pluginId, e);
        }
    }

    @Override
    public void disableHotReload() {
        if (!hotReloadEnabled) {
            return;
        }

        try {
            if (watchThread != null) {
                watchThread.interrupt();
                watchThread = null;
            }
            if (watchService != null) {
                watchService.close();
                watchService = null;
            }
            hotReloadEnabled = false;
            log.info("Disabled plugin hot reload");
        } catch (IOException e) {
            log.error("Failed to disable hot reload", e);
        }
    }

    @Override
    public void enableHotReload(Path directory, long pollInterval) throws PluginException {
        if (hotReloadEnabled) {
            throw new PluginException("Hot reload already enabled");
        }

        try {
            this.debounceTimeMs = config.getDebounceTime();
            this.pollInterval = config.getPollInterval();
            this.watchService = FileSystems.getDefault().newWatchService();

            directory.register(watchService,
                StandardWatchEventKinds.ENTRY_CREATE,
                StandardWatchEventKinds.ENTRY_DELETE,
                StandardWatchEventKinds.ENTRY_MODIFY);

            this.watchThread = new Thread(this::watchPluginsDir, "PluginHotReload-Watcher");
            watchThread.setDaemon(true);
            watchThread.start();

            hotReloadEnabled = true;
            log.info("Enabled plugin hot reload for directory: {}", directory);
        } catch (IOException e) {
            throw new PluginException("Failed to enable hot reload", e);
        }
    }

    private void watchPluginsDir() {
        // Implementation of watch logic with debounce time
        try {
            WatchKey key;
            while ((key = watchService.take()) != null) {
                log.debug("Applying debounce delay of {}ms", debounceTimeMs);
                Thread.sleep(debounceTimeMs); // Debounce delay

                for (WatchEvent<?> event : key.pollEvents()) {
                    log.info("Detected file change: {}", event.context());
                    reloadPlugins();
                }
                key.reset();
            }
        } catch (Exception e) {
            log.error("Error in plugin hot reload watcher", e);
        }
    }

    public void enableHotReload(Path pluginsDir) {
       // this.config.setPluginsDir(pluginsDir.toString());
        try {
            enableHotReload(pluginsDir, config.getPollInterval());
        } catch (PluginException e) {
            log.error("Failed to enable hot reload for plugins directory: {}", pluginsDir, e);
        }
    }

    // Rest of the original file content...
}
