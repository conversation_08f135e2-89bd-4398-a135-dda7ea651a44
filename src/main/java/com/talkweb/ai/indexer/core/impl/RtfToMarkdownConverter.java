package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.converter.AbstractDocumentConverter;
import com.talkweb.ai.indexer.core.converter.ConversionCapabilities;
import com.talkweb.ai.indexer.core.converter.ConversionContext;
import com.talkweb.ai.indexer.core.converter.ConversionMetadata;
import com.talkweb.ai.indexer.util.rtf.RtfConversionConfig;
import com.talkweb.ai.indexer.util.rtf.RtfConversionMode;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Set;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * RTF to Markdown converter implementation using the new architecture
 * 
 * This converter wraps the existing utility converter and provides
 * the new interface while maintaining backward compatibility.
 * 
 * Features:
 * - Full compatibility with RTF format documents
 * - Comprehensive content extraction (text, formatting, tables, images)
 * - Structure preservation with proper Markdown hierarchy
 * - Configurable conversion options
 * - High-performance processing with memory optimization
 * - Robust error handling and recovery mechanisms
 * - Support for various RTF versions and encodings
 * 
 * <AUTHOR> Assistant
 * @version 3.0 (Refactored to use new architecture)
 */
public class RtfToMarkdownConverter extends AbstractDocumentConverter {
    
    private static final Logger logger = Logger.getLogger(RtfToMarkdownConverter.class.getName());
    
    /**
     * Default constructor
     */
    public RtfToMarkdownConverter() {
        super();
    }
    
    @Override
    protected ConversionMetadata createMetadata() {
        return ConversionMetadata.builder("RTF to Markdown Converter")
                .description("Converts RTF files to Markdown format with comprehensive content extraction and structure preservation")
                .version("3.0")
                .attribute("author", "AI Assistant")
                .attribute("supportedInputFormats", Set.of("rtf"))
                .attribute("supportedOutputFormats", Set.of("md"))
                .build();
    }
    
    @Override
    public Set<String> getSupportedExtensions() {
        return Set.of("rtf");
    }
    
    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.HEADINGS)
                .feature(ConversionCapabilities.Features.TABLES)
                .feature(ConversionCapabilities.Features.LISTS)
                .feature(ConversionCapabilities.Features.IMAGES)
                .feature(ConversionCapabilities.Features.METADATA)
                .capability("strictMode", true)
                .capability("looseMode", true)
                .capability("advancedParsing", true)
                .capability("structurePreservation", true)
                .capability("encodingSupport", true)
                .capability("maxFileSize", 100 * 1024 * 1024) // 100MB
                .build();
    }
    
    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) throws ConversionException {
        logger.info("Starting RTF conversion for: " + inputFile.getName());
        
        String parentDir = inputFile.getParent();
        if (parentDir == null) {
            parentDir = ".";
        }
        Path outputPath = Path.of(parentDir, inputFile.getName().replaceAll("\\.rtf$", ".md"));
        
        try {
            // Create conversion configuration from context
            RtfConversionConfig config = createConfigFromContext(context);
            
            // Use the existing utility converter
            com.talkweb.ai.indexer.util.RtfToMarkdownConverter utilConverter = 
                new com.talkweb.ai.indexer.util.RtfToMarkdownConverter(null);
            
            // Configure the utility converter
            utilConverter.configure(config);
            
            // Perform conversion
            ConversionResult result = utilConverter.convert(inputFile);
            
            // Update the output path to match our convention
            return new ConversionResult(
                result.getStatus(),
                inputFile.getPath(),
                outputPath.toString(),
                result.getContent()
            );
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "RTF conversion failed: " + inputFile.getName(), e);
            throw new ConversionException("RTF conversion failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Creates RtfConversionConfig from ConversionContext
     */
    private RtfConversionConfig createConfigFromContext(ConversionContext context) {
        RtfConversionConfig config = new RtfConversionConfig();
        
        // Apply conversion mode
        String modeStr = context.getOptions().getStringOption("conversionMode", "LOOSE");
        try {
            RtfConversionMode mode = RtfConversionMode.valueOf(modeStr.toUpperCase());
            config.setMode(mode);
        } catch (IllegalArgumentException e) {
            logger.warning("Invalid conversion mode: " + modeStr + ", using LOOSE mode");
            config.setMode(RtfConversionMode.LOOSE);
        }
        
        // Apply boolean options
        config.setUseAdvancedParsing(context.getOptions().getBooleanOption("useAdvancedParsing", true));
        config.setPreserveFormatting(context.getOptions().getBooleanOption("preserveFormatting", true));
        config.setConvertImages(context.getOptions().getBooleanOption("convertImages", false));
        config.setConvertTables(context.getOptions().getBooleanOption("convertTables", true));
        config.setConvertHyperlinks(context.getOptions().getBooleanOption("convertHyperlinks", true));
        config.setIncludeMetadata(context.getOptions().getBooleanOption("includeMetadata", false));
        config.setPreserveLineBreaks(context.getOptions().getBooleanOption("preserveLineBreaks", true));

        // Apply string options
        config.setDefaultEncoding(context.getOptions().getStringOption("defaultEncoding", "UTF-8"));

        // Apply integer options
        config.setMaxDocumentSize(context.getOptions().getIntOption("maxDocumentSize", 50 * 1024 * 1024)); // 50MB default
        
        return config;
    }
    
    /**
     * Validates RTF file format
     */
    @Override
    protected void validateInput(File inputFile, ConversionContext context) throws ConversionException {
        super.validateInput(inputFile, context);

        String fileName = inputFile.getName().toLowerCase();
        if (!fileName.endsWith(".rtf")) {
            throw new ConversionException("Unsupported file format. Only .rtf files are supported.");
        }

        // Additional validation for file size
        long fileSize = inputFile.length();
        long maxSize = 100L * 1024 * 1024; // 100MB default

        if (fileSize > maxSize) {
            throw new ConversionException("File size (" + fileSize + " bytes) exceeds maximum allowed size (" + maxSize + " bytes)");
        }

        // Check if file is readable
        if (!inputFile.canRead()) {
            throw new ConversionException("Cannot read input file: " + inputFile.getPath());
        }

        // Basic RTF format validation
        try (FileInputStream fis = new FileInputStream(inputFile)) {
            byte[] header = new byte[5];
            int bytesRead = fis.read(header);

            if (bytesRead < 5) {
                throw new ConversionException("File is too small to be a valid RTF document");
            }

            String headerStr = new String(header, "ASCII");
            if (!headerStr.equals("{\\rtf")) {
                throw new ConversionException("File does not appear to be a valid RTF document (missing RTF signature)");
            }

        } catch (IOException e) {
            throw new ConversionException("Failed to validate RTF file: " + e.getMessage(), e);
        }
    }
    
    /**
     * Gets conversion statistics
     */
    public ConversionStatistics getLastConversionStatistics() {
        // This would be implemented to return statistics from the last conversion
        // For now, return a placeholder
        return new ConversionStatistics();
    }
    
    /**
     * Conversion statistics holder
     */
    public static class ConversionStatistics {
        private int paragraphCount = 0;
        private int tableCount = 0;
        private int imageCount = 0;
        private int hyperlinkCount = 0;
        private int characterCount = 0;
        private long processingTimeMs = 0;
        
        // Getters and setters
        public int getParagraphCount() { return paragraphCount; }
        public void setParagraphCount(int paragraphCount) { this.paragraphCount = paragraphCount; }
        
        public int getTableCount() { return tableCount; }
        public void setTableCount(int tableCount) { this.tableCount = tableCount; }
        
        public int getImageCount() { return imageCount; }
        public void setImageCount(int imageCount) { this.imageCount = imageCount; }
        
        public int getHyperlinkCount() { return hyperlinkCount; }
        public void setHyperlinkCount(int hyperlinkCount) { this.hyperlinkCount = hyperlinkCount; }
        
        public int getCharacterCount() { return characterCount; }
        public void setCharacterCount(int characterCount) { this.characterCount = characterCount; }
        
        public long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
        
        @Override
        public String toString() {
            return String.format("ConversionStatistics{paragraphs=%d, tables=%d, images=%d, hyperlinks=%d, characters=%d, time=%dms}",
                    paragraphCount, tableCount, imageCount, hyperlinkCount, characterCount, processingTimeMs);
        }
    }
}
