package com.talkweb.ai.indexer.core.hotreload;

import java.nio.file.Path;
import java.util.List;
import java.util.Set;

/**
 * 热重载管理器接口
 * 负责监控插件文件变化并执行增量重载
 */
public interface HotReloadManager {

    /**
     * 启动热重载监控
     * 
     * @param watchDirectories 要监控的目录列表
     * @throws HotReloadException 如果启动失败
     */
    void start(List<Path> watchDirectories) throws HotReloadException;

    /**
     * 停止热重载监控
     */
    void stop();

    /**
     * 检查热重载是否正在运行
     * 
     * @return 如果正在运行返回true
     */
    boolean isRunning();

    /**
     * 添加监控目录
     * 
     * @param directory 要监控的目录
     * @throws HotReloadException 如果添加失败
     */
    void addWatchDirectory(Path directory) throws HotReloadException;

    /**
     * 移除监控目录
     * 
     * @param directory 要移除的目录
     */
    void removeWatchDirectory(Path directory);

    /**
     * 获取当前监控的目录
     * 
     * @return 监控目录集合
     */
    Set<Path> getWatchDirectories();

    /**
     * 手动触发重载
     * 
     * @param pluginPath 插件文件路径
     * @throws HotReloadException 如果重载失败
     */
    void triggerReload(Path pluginPath) throws HotReloadException;

    /**
     * 添加热重载监听器
     * 
     * @param listener 监听器
     */
    void addHotReloadListener(HotReloadListener listener);

    /**
     * 移除热重载监听器
     * 
     * @param listener 监听器
     */
    void removeHotReloadListener(HotReloadListener listener);

    /**
     * 设置防抖延迟时间
     * 
     * @param delayMs 延迟时间（毫秒）
     */
    void setDebounceDelay(long delayMs);

    /**
     * 获取防抖延迟时间
     * 
     * @return 延迟时间（毫秒）
     */
    long getDebounceDelay();

    /**
     * 设置支持的文件扩展名
     * 
     * @param extensions 文件扩展名集合
     */
    void setSupportedExtensions(Set<String> extensions);

    /**
     * 获取支持的文件扩展名
     * 
     * @return 文件扩展名集合
     */
    Set<String> getSupportedExtensions();

    /**
     * 获取热重载统计信息
     * 
     * @return 统计信息
     */
    HotReloadStatistics getStatistics();

    /**
     * 清除统计信息
     */
    void clearStatistics();

    /**
     * 热重载统计信息
     */
    class HotReloadStatistics {
        private final long totalReloads;
        private final long successfulReloads;
        private final long failedReloads;
        private final long averageReloadTime;
        private final long lastReloadTime;
        private final int watchedDirectories;
        private final int watchedFiles;

        public HotReloadStatistics(long totalReloads, long successfulReloads, long failedReloads,
                                 long averageReloadTime, long lastReloadTime, 
                                 int watchedDirectories, int watchedFiles) {
            this.totalReloads = totalReloads;
            this.successfulReloads = successfulReloads;
            this.failedReloads = failedReloads;
            this.averageReloadTime = averageReloadTime;
            this.lastReloadTime = lastReloadTime;
            this.watchedDirectories = watchedDirectories;
            this.watchedFiles = watchedFiles;
        }

        public long getTotalReloads() { return totalReloads; }
        public long getSuccessfulReloads() { return successfulReloads; }
        public long getFailedReloads() { return failedReloads; }
        public long getAverageReloadTime() { return averageReloadTime; }
        public long getLastReloadTime() { return lastReloadTime; }
        public int getWatchedDirectories() { return watchedDirectories; }
        public int getWatchedFiles() { return watchedFiles; }

        public double getSuccessRate() {
            return totalReloads > 0 ? (double) successfulReloads / totalReloads * 100 : 0;
        }
    }
}
