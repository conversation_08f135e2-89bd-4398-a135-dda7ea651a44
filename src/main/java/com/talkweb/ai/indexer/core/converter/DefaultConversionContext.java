package com.talkweb.ai.indexer.core.converter;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认的转换上下文实现
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class DefaultConversionContext implements ConversionContext {
    
    private final String mode;
    private final ConversionOptions options;
    private final Map<String, Object> properties;
    private final Map<String, Object> cache;
    
    /**
     * 创建默认的转换上下文
     * 
     * @param mode 转换模式
     * @param options 转换选项
     * @param properties 属性映射
     */
    public DefaultConversionContext(String mode, ConversionOptions options, Map<String, Object> properties) {
        this.mode = mode;
        this.options = options != null ? options : ConversionOptions.builder().build();
        this.properties = new HashMap<>(properties);
        this.cache = new ConcurrentHashMap<>();
    }
    
    @Override
    public Optional<Object> getProperty(String key) {
        return Optional.ofNullable(properties.get(key));
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public <T> T getProperty(String key, T defaultValue) {
        Object value = properties.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return (T) value;
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }
    
    @Override
    public ConversionContext setProperty(String key, Object value) {
        properties.put(key, value);
        return this;
    }
    
    @Override
    public Map<String, Object> getProperties() {
        return Collections.unmodifiableMap(properties);
    }
    
    @Override
    public Optional<Object> getCache(String key) {
        return Optional.ofNullable(cache.get(key));
    }
    
    @Override
    public ConversionContext setCache(String key, Object value) {
        cache.put(key, value);
        return this;
    }
    
    @Override
    public ConversionContext clearCache() {
        cache.clear();
        return this;
    }
    
    @Override
    public String getMode() {
        return mode;
    }
    
    @Override
    public ConversionOptions getOptions() {
        return options;
    }
    
    /**
     * 默认的上下文构建器实现
     */
    public static class Builder implements ConversionContext.Builder {
        private String mode = "DEFAULT";
        private ConversionOptions options;
        private final Map<String, Object> properties = new HashMap<>();
        
        @Override
        public Builder mode(String mode) {
            this.mode = mode;
            return this;
        }
        
        @Override
        public Builder options(ConversionOptions options) {
            this.options = options;
            return this;
        }
        
        @Override
        public Builder property(String key, Object value) {
            properties.put(key, value);
            return this;
        }
        
        @Override
        public ConversionContext build() {
            if (options == null) {
                options = ConversionOptions.builder().build();
            }
            return new DefaultConversionContext(mode, options, properties);
        }
    }
}