package com.talkweb.ai.indexer.core.isolation;

import com.talkweb.ai.indexer.core.classloader.IsolatedPluginClassLoader;
import com.talkweb.ai.indexer.core.security.PluginSandbox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 插件隔离上下文
 * 封装插件的隔离环境信息
 */
public class PluginIsolationContext {
    
    private static final Logger log = LoggerFactory.getLogger(PluginIsolationContext.class);
    
    private final String pluginId;
    private final IsolatedPluginClassLoader classLoader;
    private final PluginSandbox sandbox;
    private final PluginIsolationManager isolationManager;
    private final long creationTime;
    private final AtomicBoolean destroyed = new AtomicBoolean(false);

    public PluginIsolationContext(String pluginId, IsolatedPluginClassLoader classLoader, 
                                PluginSandbox sandbox, PluginIsolationManager isolationManager) {
        this.pluginId = pluginId;
        this.classLoader = classLoader;
        this.sandbox = sandbox;
        this.isolationManager = isolationManager;
        this.creationTime = System.currentTimeMillis();
        
        log.debug("Created isolation context for plugin: {}", pluginId);
    }

    /**
     * 获取插件ID
     */
    public String getPluginId() {
        return pluginId;
    }

    /**
     * 获取隔离的类加载器
     */
    public IsolatedPluginClassLoader getClassLoader() {
        checkNotDestroyed();
        return classLoader;
    }

    /**
     * 获取插件沙箱
     */
    public PluginSandbox getSandbox() {
        checkNotDestroyed();
        return sandbox;
    }

    /**
     * 获取隔离管理器
     */
    public PluginIsolationManager getIsolationManager() {
        return isolationManager;
    }

    /**
     * 获取创建时间
     */
    public long getCreationTime() {
        return creationTime;
    }

    /**
     * 检查是否已销毁
     */
    public boolean isDestroyed() {
        return destroyed.get();
    }

    /**
     * 在隔离环境中执行代码
     */
    public <T> T executeInIsolation(IsolatedExecution<T> execution) throws Exception {
        checkNotDestroyed();
        
        ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
        try {
            // 设置插件的类加载器为当前线程的上下文类加载器
            Thread.currentThread().setContextClassLoader(classLoader);
            
            log.debug("Executing code in isolation for plugin: {}", pluginId);
            return execution.execute();
            
        } finally {
            // 恢复原始的类加载器
            Thread.currentThread().setContextClassLoader(originalClassLoader);
        }
    }

    /**
     * 在隔离环境中执行代码（无返回值）
     */
    public void executeInIsolation(IsolatedExecutionVoid execution) throws Exception {
        executeInIsolation(() -> {
            execution.execute();
            return null;
        });
    }

    /**
     * 配置与其他插件的通信权限
     */
    public void allowCommunicationWith(String targetPluginId) {
        checkNotDestroyed();
        isolationManager.configurePluginCommunication(pluginId, targetPluginId, true);
    }

    /**
     * 禁止与其他插件的通信
     */
    public void blockCommunicationWith(String targetPluginId) {
        checkNotDestroyed();
        isolationManager.configurePluginCommunication(pluginId, targetPluginId, false);
    }

    /**
     * 添加共享包
     */
    public void addSharedPackage(String packageName) {
        checkNotDestroyed();
        classLoader.addSharedPackage(packageName);
    }

    /**
     * 添加允许的包
     */
    public void addAllowedPackage(String packageName) {
        checkNotDestroyed();
        classLoader.addAllowedPackage(packageName);
    }

    /**
     * 添加阻止的包
     */
    public void addBlockedPackage(String packageName) {
        checkNotDestroyed();
        classLoader.addBlockedPackage(packageName);
    }

    /**
     * 获取隔离上下文信息
     */
    public IsolationContextInfo getContextInfo() {
        return new IsolationContextInfo(
            pluginId,
            creationTime,
            System.currentTimeMillis() - creationTime,
            !destroyed.get(),
            classLoader.toString(),
            sandbox.getSandboxId()
        );
    }

    /**
     * 销毁隔离上下文
     */
    public void destroy() {
        if (destroyed.compareAndSet(false, true)) {
            try {
                log.info("Destroying isolation context for plugin: {}", pluginId);
                
                // 销毁沙箱
                if (sandbox != null && !sandbox.isDestroyed()) {
                    sandbox.destroy();
                }
                
                // 关闭类加载器
                if (classLoader != null) {
                    classLoader.close();
                }
                
                log.info("Successfully destroyed isolation context for plugin: {}", pluginId);
                
            } catch (Exception e) {
                log.error("Error destroying isolation context for plugin: {}", pluginId, e);
            }
        }
    }

    /**
     * 检查上下文是否已销毁
     */
    private void checkNotDestroyed() {
        if (destroyed.get()) {
            throw new IllegalStateException("Isolation context has been destroyed for plugin: " + pluginId);
        }
    }

    /**
     * 隔离执行接口（有返回值）
     */
    @FunctionalInterface
    public interface IsolatedExecution<T> {
        T execute() throws Exception;
    }

    /**
     * 隔离执行接口（无返回值）
     */
    @FunctionalInterface
    public interface IsolatedExecutionVoid {
        void execute() throws Exception;
    }

    /**
     * 隔离上下文信息
     */
    public static class IsolationContextInfo {
        private final String pluginId;
        private final long creationTime;
        private final long uptime;
        private final boolean active;
        private final String classLoaderInfo;
        private final String sandboxId;

        public IsolationContextInfo(String pluginId, long creationTime, long uptime, 
                                  boolean active, String classLoaderInfo, String sandboxId) {
            this.pluginId = pluginId;
            this.creationTime = creationTime;
            this.uptime = uptime;
            this.active = active;
            this.classLoaderInfo = classLoaderInfo;
            this.sandboxId = sandboxId;
        }

        public String getPluginId() { return pluginId; }
        public long getCreationTime() { return creationTime; }
        public long getUptime() { return uptime; }
        public boolean isActive() { return active; }
        public String getClassLoaderInfo() { return classLoaderInfo; }
        public String getSandboxId() { return sandboxId; }

        @Override
        public String toString() {
            return String.format("IsolationContextInfo{pluginId='%s', uptime=%dms, active=%s, sandbox='%s'}",
                               pluginId, uptime, active, sandboxId);
        }
    }
}
