package com.talkweb.ai.indexer.core.util;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 转换缓存管理器，用于统一管理转换过程中的缓存
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConversionCacheManager {
    
    // 缓存实例映射
    private static final Map<String, Cache<?, ?>> caches = new ConcurrentHashMap<>();
    
    /**
     * 获取缓存实例
     * 
     * @param name 缓存名称
     * @param config 缓存配置
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 缓存实例
     */
    public static <K, V> Cache<K, V> getCache(String name, CacheConfig config) {
        @SuppressWarnings("unchecked")
        Cache<K, V> cache = (Cache<K, V>) caches.computeIfAbsent(name, k -> {
            switch (config.getType()) {
                case LRU:
                    return new LruCache<K, V>(config.getMaxSize(), config.getExpireAfterWrite(), config.getTimeUnit());
                case LFU:
                    return new LfuCache<K, V>(config.getMaxSize(), config.getExpireAfterWrite(), config.getTimeUnit());
                case SIMPLE:
                default:
                    return new SimpleCache<K, V>(config.getMaxSize(), config.getExpireAfterWrite(), config.getTimeUnit());
            }
        });
        
        return cache;
    }
    
    /**
     * 清除所有缓存
     */
    public static void clearAllCaches() {
        caches.values().forEach(Cache::clear);
    }

    /**
     * 减少所有缓存的大小
     *
     * @param factor 缩减因子 (0.0 - 1.0)
     */
    public static void reduceCacheSizes(double factor) {
        if (factor <= 0 || factor > 1) {
            throw new IllegalArgumentException("Factor must be between 0 and 1");
        }

        caches.values().forEach(cache -> {
            if (cache instanceof ResizableCache) {
                ((ResizableCache) cache).resize(factor);
            }
        });
    }

    /**
     * 增加所有缓存的大小
     *
     * @param factor 增长因子 (> 1.0)
     */
    public static void increaseCacheSizes(double factor) {
        if (factor <= 1) {
            throw new IllegalArgumentException("Factor must be greater than 1");
        }

        caches.values().forEach(cache -> {
            if (cache instanceof ResizableCache) {
                ((ResizableCache) cache).resize(factor);
            }
        });
    }

    /**
     * 获取所有缓存的统计信息
     */
    public static CacheStatistics getAllCacheStatistics() {
        long totalSize = 0;
        long totalHits = 0;
        long totalMisses = 0;

        for (Cache<?, ?> cache : caches.values()) {
            if (cache instanceof StatisticsAwareCache) {
                StatisticsAwareCache statsCache = (StatisticsAwareCache) cache;
                totalSize += statsCache.size();
                totalHits += statsCache.getHitCount();
                totalMisses += statsCache.getMissCount();
            }
        }

        double hitRate = (totalHits + totalMisses) > 0 ?
            (double) totalHits / (totalHits + totalMisses) : 0.0;

        return new CacheStatistics((int)totalSize, 0, (int)totalHits, (int)totalMisses, hitRate);
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息映射
     */
    public static Map<String, CacheStatistics> getStatistics() {
        Map<String, CacheStatistics> statistics = new HashMap<>();
        
        caches.forEach((name, cache) -> {
            statistics.put(name, cache.getStatistics());
        });
        
        return Collections.unmodifiableMap(statistics);
    }
    
    /**
     * 缓存接口
     * 
     * @param <K> 键类型
     * @param <V> 值类型
     */
    public interface Cache<K, V> {
        /**
         * 获取缓存值
         * 
         * @param key 键
         * @return 值，如果不存在则返回null
         */
        V get(K key);
        
        /**
         * 设置缓存值
         * 
         * @param key 键
         * @param value 值
         * @return 当前缓存实例
         */
        Cache<K, V> put(K key, V value);
        
        /**
         * 移除缓存值
         * 
         * @param key 键
         * @return 被移除的值，如果不存在则返回null
         */
        V remove(K key);
        
        /**
         * 清除所有缓存
         */
        void clear();
        
        /**
         * 获取缓存大小
         * 
         * @return 缓存大小
         */
        int size();
        
        /**
         * 获取缓存统计信息
         * 
         * @return 缓存统计信息
         */
        CacheStatistics getStatistics();
    }
    
    /**
     * 简单缓存实现
     * 
     * @param <K> 键类型
     * @param <V> 值类型
     */
    private static class SimpleCache<K, V> implements Cache<K, V> {
        private final Map<K, CacheEntry<V>> map = new ConcurrentHashMap<>();
        private final int maxSize;
        private final long expireAfterWrite;
        private final TimeUnit timeUnit;
        
        private int hits = 0;
        private int misses = 0;
        
        public SimpleCache(int maxSize, long expireAfterWrite, TimeUnit timeUnit) {
            this.maxSize = maxSize > 0 ? maxSize : Integer.MAX_VALUE;
            this.expireAfterWrite = expireAfterWrite;
            this.timeUnit = timeUnit;
        }
        
        @Override
        public V get(K key) {
            CacheEntry<V> entry = map.get(key);
            
            if (entry == null) {
                misses++;
                return null;
            }
            
            if (isExpired(entry)) {
                map.remove(key);
                misses++;
                return null;
            }
            
            hits++;
            return entry.getValue();
        }
        
        @Override
        public Cache<K, V> put(K key, V value) {
            if (map.size() >= maxSize && !map.containsKey(key)) {
                // 缓存已满，需要淘汰
                evict();
            }
            
            map.put(key, new CacheEntry<>(value, System.nanoTime()));
            return this;
        }
        
        @Override
        public V remove(K key) {
            CacheEntry<V> entry = map.remove(key);
            return entry != null ? entry.getValue() : null;
        }
        
        @Override
        public void clear() {
            map.clear();
            hits = 0;
            misses = 0;
        }
        
        @Override
        public int size() {
            return map.size();
        }
        
        @Override
        public CacheStatistics getStatistics() {
            return new CacheStatistics(
                    map.size(),
                    maxSize,
                    hits,
                    misses,
                    (double) hits / (hits + misses > 0 ? hits + misses : 1)
            );
        }
        
        /**
         * 检查缓存项是否过期
         * 
         * @param entry 缓存项
         * @return 如果过期返回true，否则返回false
         */
        private boolean isExpired(CacheEntry<V> entry) {
            if (expireAfterWrite <= 0) {
                return false;
            }
            
            long now = System.nanoTime();
            long expireTime = entry.getTimestamp() + timeUnit.toNanos(expireAfterWrite);
            
            return now > expireTime;
        }
        
        /**
         * 淘汰策略
         */
        private void evict() {
            // 简单实现：随机淘汰一个
            if (!map.isEmpty()) {
                K keyToRemove = map.keySet().iterator().next();
                map.remove(keyToRemove);
            }
        }
    }
    
    /**
     * LRU缓存实现
     * 
     * @param <K> 键类型
     * @param <V> 值类型
     */
    private static class LruCache<K, V> extends SimpleCache<K, V> {
        public LruCache(int maxSize, long expireAfterWrite, TimeUnit timeUnit) {
            super(maxSize, expireAfterWrite, timeUnit);
        }
        
        // 这里简化实现，实际应该使用LinkedHashMap或自定义的LRU算法
    }
    
    /**
     * LFU缓存实现
     * 
     * @param <K> 键类型
     * @param <V> 值类型
     */
    private static class LfuCache<K, V> extends SimpleCache<K, V> {
        public LfuCache(int maxSize, long expireAfterWrite, TimeUnit timeUnit) {
            super(maxSize, expireAfterWrite, timeUnit);
        }
        
        // 这里简化实现，实际应该使用自定义的LFU算法
    }
    
    /**
     * 缓存项
     * 
     * @param <V> 值类型
     */
    private static class CacheEntry<V> {
        private final V value;
        private final long timestamp;
        
        public CacheEntry(V value, long timestamp) {
            this.value = value;
            this.timestamp = timestamp;
        }
        
        public V getValue() {
            return value;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
    }
    
    /**
     * 缓存配置
     */
    public static class CacheConfig {
        private final CacheType type;
        private final int maxSize;
        private final long expireAfterWrite;
        private final TimeUnit timeUnit;
        
        private CacheConfig(CacheType type, int maxSize, long expireAfterWrite, TimeUnit timeUnit) {
            this.type = type;
            this.maxSize = maxSize;
            this.expireAfterWrite = expireAfterWrite;
            this.timeUnit = timeUnit;
        }
        
        public CacheType getType() {
            return type;
        }
        
        public int getMaxSize() {
            return maxSize;
        }
        
        public long getExpireAfterWrite() {
            return expireAfterWrite;
        }
        
        public TimeUnit getTimeUnit() {
            return timeUnit;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private CacheType type = CacheType.SIMPLE;
            private int maxSize = 1000;
            private long expireAfterWrite = 0;
            private TimeUnit timeUnit = TimeUnit.SECONDS;
            
            public Builder type(CacheType type) {
                this.type = type;
                return this;
            }
            
            public Builder maxSize(int maxSize) {
                this.maxSize = maxSize;
                return this;
            }
            
            public Builder expireAfterWrite(long expireAfterWrite, TimeUnit timeUnit) {
                this.expireAfterWrite = expireAfterWrite;
                this.timeUnit = timeUnit;
                return this;
            }
            
            public CacheConfig build() {
                return new CacheConfig(type, maxSize, expireAfterWrite, timeUnit);
            }
        }
    }
    
    /**
     * 缓存类型
     */
    public enum CacheType {
        SIMPLE,
        LRU,
        LFU
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStatistics {
        private final int size;
        private final int maxSize;
        private final int hits;
        private final int misses;
        private final double hitRate;
        
        public CacheStatistics(int size, int maxSize, int hits, int misses, double hitRate) {
            this.size = size;
            this.maxSize = maxSize;
            this.hits = hits;
            this.misses = misses;
            this.hitRate = hitRate;
        }
        
        public int getSize() {
            return size;
        }
        
        public int getMaxSize() {
            return maxSize;
        }
        
        public int getHits() {
            return hits;
        }
        
        public int getMisses() {
            return misses;
        }
        
        public double getHitRate() {
            return hitRate;
        }
        
        @Override
        public String toString() {
            return String.format(
                    "CacheStatistics{size=%d, maxSize=%d, hits=%d, misses=%d, hitRate=%.2f}",
                    size, maxSize, hits, misses, hitRate
            );
        }
    }
}