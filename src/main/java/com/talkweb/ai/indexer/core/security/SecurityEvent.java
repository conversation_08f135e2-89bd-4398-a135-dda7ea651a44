package com.talkweb.ai.indexer.core.security;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 安全事件
 * 记录插件安全相关的事件
 */
public class SecurityEvent {
    
    private final String eventId;
    private final String pluginId;
    private final EventType eventType;
    private final EventLevel level;
    private final String message;
    private final String details;
    private final LocalDateTime timestamp;
    private final String source;
    private final Map<String, Object> metadata;

    public SecurityEvent(String eventId, String pluginId, EventType eventType, EventLevel level,
                        String message, String details, LocalDateTime timestamp, String source,
                        Map<String, Object> metadata) {
        this.eventId = eventId;
        this.pluginId = pluginId;
        this.eventType = eventType;
        this.level = level;
        this.message = message;
        this.details = details;
        this.timestamp = timestamp;
        this.source = source;
        this.metadata = metadata;
    }

    public String getEventId() {
        return eventId;
    }

    public String getPluginId() {
        return pluginId;
    }

    public EventType getEventType() {
        return eventType;
    }

    public EventLevel getLevel() {
        return level;
    }

    public String getMessage() {
        return message;
    }

    public String getDetails() {
        return details;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public String getSource() {
        return source;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    /**
     * 事件类型
     */
    public enum EventType {
        PLUGIN_LOADED,
        PLUGIN_UNLOADED,
        SIGNATURE_VERIFIED,
        SIGNATURE_VERIFICATION_FAILED,
        PERMISSION_GRANTED,
        PERMISSION_DENIED,
        SANDBOX_CREATED,
        SANDBOX_DESTROYED,
        SANDBOX_VIOLATION,
        POLICY_APPLIED,
        POLICY_VIOLATION,
        SECURITY_EXCEPTION,
        RESOURCE_LIMIT_EXCEEDED,
        UNTRUSTED_OPERATION
    }

    /**
     * 事件级别
     */
    public enum EventLevel {
        INFO,
        WARNING,
        ERROR,
        CRITICAL
    }

    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 事件构建器
     */
    public static class Builder {
        private String eventId;
        private String pluginId;
        private EventType eventType;
        private EventLevel level = EventLevel.INFO;
        private String message;
        private String details;
        private LocalDateTime timestamp = LocalDateTime.now();
        private String source;
        private Map<String, Object> metadata = Map.of();

        public Builder eventId(String eventId) {
            this.eventId = eventId;
            return this;
        }

        public Builder pluginId(String pluginId) {
            this.pluginId = pluginId;
            return this;
        }

        public Builder eventType(EventType eventType) {
            this.eventType = eventType;
            return this;
        }

        public Builder level(EventLevel level) {
            this.level = level;
            return this;
        }

        public Builder message(String message) {
            this.message = message;
            return this;
        }

        public Builder details(String details) {
            this.details = details;
            return this;
        }

        public Builder timestamp(LocalDateTime timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder source(String source) {
            this.source = source;
            return this;
        }

        public Builder metadata(Map<String, Object> metadata) {
            this.metadata = metadata;
            return this;
        }

        public SecurityEvent build() {
            return new SecurityEvent(eventId, pluginId, eventType, level, message, 
                                   details, timestamp, source, metadata);
        }
    }

    // 工厂方法用于创建常见的安全事件
    public static SecurityEvent pluginLoaded(String pluginId, String source) {
        return builder()
            .eventId(generateEventId())
            .pluginId(pluginId)
            .eventType(EventType.PLUGIN_LOADED)
            .level(EventLevel.INFO)
            .message("Plugin loaded successfully")
            .source(source)
            .build();
    }

    public static SecurityEvent signatureVerificationFailed(String pluginId, String reason) {
        return builder()
            .eventId(generateEventId())
            .pluginId(pluginId)
            .eventType(EventType.SIGNATURE_VERIFICATION_FAILED)
            .level(EventLevel.ERROR)
            .message("Signature verification failed")
            .details(reason)
            .source("PluginSecurityManager")
            .build();
    }

    public static SecurityEvent permissionDenied(String pluginId, String permission, String reason) {
        return builder()
            .eventId(generateEventId())
            .pluginId(pluginId)
            .eventType(EventType.PERMISSION_DENIED)
            .level(EventLevel.WARNING)
            .message("Permission denied: " + permission)
            .details(reason)
            .source("PluginSecurityManager")
            .metadata(Map.of("permission", permission))
            .build();
    }

    public static SecurityEvent sandboxViolation(String pluginId, String violation) {
        return builder()
            .eventId(generateEventId())
            .pluginId(pluginId)
            .eventType(EventType.SANDBOX_VIOLATION)
            .level(EventLevel.CRITICAL)
            .message("Sandbox violation detected")
            .details(violation)
            .source("PluginSandbox")
            .build();
    }

    public static SecurityEvent policyViolation(String pluginId, String policyId, String violation) {
        return builder()
            .eventId(generateEventId())
            .pluginId(pluginId)
            .eventType(EventType.POLICY_VIOLATION)
            .level(EventLevel.ERROR)
            .message("Security policy violation")
            .details(violation)
            .source("PluginSecurityManager")
            .metadata(Map.of("policyId", policyId))
            .build();
    }

    private static String generateEventId() {
        return "SEC_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
}
