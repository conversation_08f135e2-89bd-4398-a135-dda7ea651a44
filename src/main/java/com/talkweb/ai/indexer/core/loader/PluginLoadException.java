package com.talkweb.ai.indexer.core.loader;

import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginState;

import java.nio.file.Path;

/**
 * 插件加载异常
 */
public class PluginLoadException extends PluginException {
    
    private final Path pluginPath;
    private final LoadErrorType errorType;

    public PluginLoadException(String message, Path pluginPath) {
        this(message, pluginPath, LoadErrorType.UNKNOWN, null);
    }

    public PluginLoadException(String message, Path pluginPath, Throwable cause) {
        this(message, pluginPath, LoadErrorType.UNKNOWN, cause);
    }

    public PluginLoadException(String message, Path pluginPath, LoadErrorType errorType) {
        this(message, pluginPath, errorType, null);
    }

    public PluginLoadException(String message, Path pluginPath, LoadErrorType errorType, Throwable cause) {
        super(message, cause, PluginState.FAILED);
        this.pluginPath = pluginPath;
        this.errorType = errorType;
    }

    public Path getPluginPath() {
        return pluginPath;
    }

    public LoadErrorType getErrorType() {
        return errorType;
    }

    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(errorType.getDescription()).append(": ").append(getMessage());
        
        if (pluginPath != null) {
            sb.append(" (Plugin: ").append(pluginPath.getFileName()).append(")");
        }
        
        return sb.toString();
    }

    /**
     * 检查是否为可恢复的错误
     */
    public boolean isRecoverable() {
        return errorType == LoadErrorType.TEMPORARY_IO_ERROR || 
               errorType == LoadErrorType.MEMORY_ERROR ||
               errorType == LoadErrorType.NETWORK_ERROR;
    }

    /**
     * 插件加载错误类型
     */
    public enum LoadErrorType {
        FILE_NOT_FOUND("Plugin file not found"),
        INVALID_FORMAT("Invalid plugin file format"),
        CORRUPTED_FILE("Plugin file is corrupted"),
        MISSING_DESCRIPTOR("Missing plugin descriptor"),
        INVALID_DESCRIPTOR("Invalid plugin descriptor"),
        MISSING_MAIN_CLASS("Plugin main class not found"),
        CLASS_LOADING_ERROR("Error loading plugin class"),
        INSTANTIATION_ERROR("Error instantiating plugin"),
        DEPENDENCY_ERROR("Plugin dependency error"),
        SECURITY_ERROR("Plugin security validation failed"),
        VERSION_INCOMPATIBLE("Plugin version incompatible"),
        TEMPORARY_IO_ERROR("Temporary I/O error"),
        MEMORY_ERROR("Insufficient memory"),
        NETWORK_ERROR("Network error"),
        UNKNOWN("Unknown error");

        private final String description;

        LoadErrorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 工厂方法用于创建常见的异常场景
    public static PluginLoadException fileNotFound(Path pluginPath) {
        return new PluginLoadException(
            "Plugin file not found: " + pluginPath,
            pluginPath,
            LoadErrorType.FILE_NOT_FOUND
        );
    }

    public static PluginLoadException invalidFormat(Path pluginPath, String expectedFormat) {
        return new PluginLoadException(
            "Invalid plugin format. Expected: " + expectedFormat + ", File: " + pluginPath,
            pluginPath,
            LoadErrorType.INVALID_FORMAT
        );
    }

    public static PluginLoadException corruptedFile(Path pluginPath, Throwable cause) {
        return new PluginLoadException(
            "Plugin file is corrupted: " + pluginPath,
            pluginPath,
            LoadErrorType.CORRUPTED_FILE,
            cause
        );
    }

    public static PluginLoadException missingDescriptor(Path pluginPath) {
        return new PluginLoadException(
            "Missing plugin descriptor (plugin.properties): " + pluginPath,
            pluginPath,
            LoadErrorType.MISSING_DESCRIPTOR
        );
    }

    public static PluginLoadException invalidDescriptor(Path pluginPath, String reason) {
        return new PluginLoadException(
            "Invalid plugin descriptor: " + reason + " (Plugin: " + pluginPath + ")",
            pluginPath,
            LoadErrorType.INVALID_DESCRIPTOR
        );
    }

    public static PluginLoadException missingMainClass(Path pluginPath, String className) {
        return new PluginLoadException(
            "Plugin main class not found: " + className + " (Plugin: " + pluginPath + ")",
            pluginPath,
            LoadErrorType.MISSING_MAIN_CLASS
        );
    }

    public static PluginLoadException classLoadingError(Path pluginPath, String className, Throwable cause) {
        return new PluginLoadException(
            "Error loading plugin class: " + className + " (Plugin: " + pluginPath + ")",
            pluginPath,
            LoadErrorType.CLASS_LOADING_ERROR,
            cause
        );
    }

    public static PluginLoadException instantiationError(Path pluginPath, String className, Throwable cause) {
        return new PluginLoadException(
            "Error instantiating plugin class: " + className + " (Plugin: " + pluginPath + ")",
            pluginPath,
            LoadErrorType.INSTANTIATION_ERROR,
            cause
        );
    }
}
