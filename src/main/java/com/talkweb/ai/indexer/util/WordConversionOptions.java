package com.talkweb.ai.indexer.util;

/**
 * Configuration options for Word to Markdown conversion
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordConversionOptions {
    
    /**
     * Image formats supported for extraction
     */
    public enum ImageFormat {
        PNG, JPEG, GIF, BMP, ORIGINAL
    }
    
    private boolean extractImages = true;
    private String imageOutputDir = "images/";
    private boolean preserveTableFormatting = true;
    private boolean convertFootnotes = true;
    private boolean includePageBreaks = false;
    private boolean includeComments = false;
    private boolean preserveHyperlinks = true;
    private ImageFormat preferredImageFormat = ImageFormat.PNG;
    private int maxImageWidth = 800;
    private int maxImageHeight = 600;
    private boolean generateImageAltText = true;
    private boolean preserveListFormatting = true;
    private boolean convertHeadersFooters = false;
    private boolean preserveTextFormatting = true;
    private boolean skipEmptyParagraphs = true;
    
    /**
     * Default constructor with sensible defaults
     */
    public WordConversionOptions() {
        // Use default values
    }
    
    /**
     * Copy constructor
     */
    public WordConversionOptions(WordConversionOptions other) {
        this.extractImages = other.extractImages;
        this.imageOutputDir = other.imageOutputDir;
        this.preserveTableFormatting = other.preserveTableFormatting;
        this.convertFootnotes = other.convertFootnotes;
        this.includePageBreaks = other.includePageBreaks;
        this.includeComments = other.includeComments;
        this.preserveHyperlinks = other.preserveHyperlinks;
        this.preferredImageFormat = other.preferredImageFormat;
        this.maxImageWidth = other.maxImageWidth;
        this.maxImageHeight = other.maxImageHeight;
        this.generateImageAltText = other.generateImageAltText;
        this.preserveListFormatting = other.preserveListFormatting;
        this.convertHeadersFooters = other.convertHeadersFooters;
        this.preserveTextFormatting = other.preserveTextFormatting;
        this.skipEmptyParagraphs = other.skipEmptyParagraphs;
    }
    
    // Getters and setters
    
    public boolean isExtractImages() {
        return extractImages;
    }
    
    public WordConversionOptions setExtractImages(boolean extractImages) {
        this.extractImages = extractImages;
        return this;
    }
    
    public String getImageOutputDir() {
        return imageOutputDir;
    }
    
    public WordConversionOptions setImageOutputDir(String imageOutputDir) {
        this.imageOutputDir = imageOutputDir != null ? imageOutputDir : "images/";
        return this;
    }
    
    public boolean isPreserveTableFormatting() {
        return preserveTableFormatting;
    }
    
    public WordConversionOptions setPreserveTableFormatting(boolean preserveTableFormatting) {
        this.preserveTableFormatting = preserveTableFormatting;
        return this;
    }
    
    public boolean isConvertFootnotes() {
        return convertFootnotes;
    }
    
    public WordConversionOptions setConvertFootnotes(boolean convertFootnotes) {
        this.convertFootnotes = convertFootnotes;
        return this;
    }
    
    public boolean isIncludePageBreaks() {
        return includePageBreaks;
    }
    
    public WordConversionOptions setIncludePageBreaks(boolean includePageBreaks) {
        this.includePageBreaks = includePageBreaks;
        return this;
    }
    
    public boolean isIncludeComments() {
        return includeComments;
    }
    
    public WordConversionOptions setIncludeComments(boolean includeComments) {
        this.includeComments = includeComments;
        return this;
    }
    
    public boolean isPreserveHyperlinks() {
        return preserveHyperlinks;
    }
    
    public WordConversionOptions setPreserveHyperlinks(boolean preserveHyperlinks) {
        this.preserveHyperlinks = preserveHyperlinks;
        return this;
    }
    
    public ImageFormat getPreferredImageFormat() {
        return preferredImageFormat;
    }
    
    public WordConversionOptions setPreferredImageFormat(ImageFormat preferredImageFormat) {
        this.preferredImageFormat = preferredImageFormat != null ? preferredImageFormat : ImageFormat.PNG;
        return this;
    }
    
    public int getMaxImageWidth() {
        return maxImageWidth;
    }
    
    public WordConversionOptions setMaxImageWidth(int maxImageWidth) {
        this.maxImageWidth = Math.max(100, maxImageWidth);
        return this;
    }
    
    public int getMaxImageHeight() {
        return maxImageHeight;
    }
    
    public WordConversionOptions setMaxImageHeight(int maxImageHeight) {
        this.maxImageHeight = Math.max(100, maxImageHeight);
        return this;
    }
    
    public boolean isGenerateImageAltText() {
        return generateImageAltText;
    }
    
    public WordConversionOptions setGenerateImageAltText(boolean generateImageAltText) {
        this.generateImageAltText = generateImageAltText;
        return this;
    }
    
    public boolean isPreserveListFormatting() {
        return preserveListFormatting;
    }
    
    public WordConversionOptions setPreserveListFormatting(boolean preserveListFormatting) {
        this.preserveListFormatting = preserveListFormatting;
        return this;
    }
    
    public boolean isConvertHeadersFooters() {
        return convertHeadersFooters;
    }
    
    public WordConversionOptions setConvertHeadersFooters(boolean convertHeadersFooters) {
        this.convertHeadersFooters = convertHeadersFooters;
        return this;
    }
    
    public boolean isPreserveTextFormatting() {
        return preserveTextFormatting;
    }
    
    public WordConversionOptions setPreserveTextFormatting(boolean preserveTextFormatting) {
        this.preserveTextFormatting = preserveTextFormatting;
        return this;
    }
    
    public boolean isSkipEmptyParagraphs() {
        return skipEmptyParagraphs;
    }
    
    public WordConversionOptions setSkipEmptyParagraphs(boolean skipEmptyParagraphs) {
        this.skipEmptyParagraphs = skipEmptyParagraphs;
        return this;
    }
    
    /**
     * Creates a preset for high-fidelity conversion
     */
    public static WordConversionOptions highFidelity() {
        return new WordConversionOptions()
                .setExtractImages(true)
                .setPreserveTableFormatting(true)
                .setConvertFootnotes(true)
                .setPreserveHyperlinks(true)
                .setPreserveListFormatting(true)
                .setPreserveTextFormatting(true)
                .setIncludeComments(true);
    }
    
    /**
     * Creates a preset for fast, basic conversion
     */
    public static WordConversionOptions basic() {
        return new WordConversionOptions()
                .setExtractImages(false)
                .setPreserveTableFormatting(false)
                .setConvertFootnotes(false)
                .setPreserveHyperlinks(false)
                .setIncludeComments(false)
                .setPreserveTextFormatting(false);
    }
    
    @Override
    public String toString() {
        return "WordConversionOptions{" +
                "extractImages=" + extractImages +
                ", imageOutputDir='" + imageOutputDir + '\'' +
                ", preserveTableFormatting=" + preserveTableFormatting +
                ", convertFootnotes=" + convertFootnotes +
                ", includePageBreaks=" + includePageBreaks +
                ", includeComments=" + includeComments +
                ", preserveHyperlinks=" + preserveHyperlinks +
                ", preferredImageFormat=" + preferredImageFormat +
                ", maxImageWidth=" + maxImageWidth +
                ", maxImageHeight=" + maxImageHeight +
                ", generateImageAltText=" + generateImageAltText +
                ", preserveListFormatting=" + preserveListFormatting +
                ", convertHeadersFooters=" + convertHeadersFooters +
                ", preserveTextFormatting=" + preserveTextFormatting +
                ", skipEmptyParagraphs=" + skipEmptyParagraphs +
                '}';
    }
}
