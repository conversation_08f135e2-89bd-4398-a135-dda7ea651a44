package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.core.*;
import com.talkweb.ai.indexer.util.ppt.*;
import com.talkweb.ai.indexer.util.ppt.converter.*;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.sl.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * Enhanced PowerPoint to Markdown converter with comprehensive compatibility and structure preservation
 *
 * Features:
 * - Full compatibility with PPT, PPTX, and PPTM formats
 * - Comprehensive slide content extraction (text, images, tables, charts)
 * - Structure preservation with proper Markdown hierarchy
 * - Configurable conversion options
 * - High-performance processing with memory optimization
 * - Robust error handling and recovery mechanisms
 * - Support for speaker notes and slide metadata
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class PptToMarkdownConverter implements DocumentConverter, Plugin {

    private static final Logger logger = LoggerFactory.getLogger(PptToMarkdownConverter.class);
    
    // Supported file extensions
    private static final String[] SUPPORTED_EXTENSIONS = {"ppt", "pptx", "pptm"};
    
    // File format signatures for detection
    private static final byte[] PPT_SIGNATURE = {(byte) 0xD0, (byte) 0xCF, (byte) 0x11, (byte) 0xE0};
    private static final byte[] PPTX_SIGNATURE = {0x50, 0x4B, 0x03, 0x04}; // ZIP signature
    
    private final PluginMetadata metadata;
    private PluginState state = PluginState.STOPPED;
    private PptConversionConfig config = new PptConversionConfig();

    /**
     * Default constructor for standalone usage
     */
    public PptToMarkdownConverter() {
        this.metadata = new PluginMetadata(
            "ppt-to-markdown-converter",
            "PowerPoint to Markdown Converter",
            "1.0.0",
            "Converts PowerPoint presentations to Markdown format",
            "AI Assistant",
            "com.talkweb.ai.indexer.util.PptToMarkdownConverter"
        );
    }

    /**
     * Constructor with plugin metadata
     */
    public PptToMarkdownConverter(PluginMetadata metadata) {
        this.metadata = metadata != null ? metadata : new PluginMetadata(
            "ppt-to-markdown-converter",
            "PowerPoint to Markdown Converter",
            "1.0.0",
            "Converts PowerPoint presentations to Markdown format",
            "AI Assistant",
            "com.talkweb.ai.indexer.util.PptToMarkdownConverter"
        );
    }

    /**
     * Converts PowerPoint file to Markdown using default configuration
     */
    public static String convertToMarkdown(File pptFile) throws IOException {
        return convertToMarkdown(pptFile, new PptConversionConfig());
    }

    /**
     * Converts PowerPoint file to Markdown with custom configuration
     */
    public static String convertToMarkdown(File pptFile, PptConversionConfig config) throws IOException {
        if (pptFile == null || !pptFile.exists()) {
            throw new IllegalArgumentException("PowerPoint file does not exist: " + pptFile);
        }

        if (!isValidPptFile(pptFile)) {
            throw new IllegalArgumentException("File is not a valid PowerPoint file: " + pptFile.getName());
        }

        logger.info("Converting PowerPoint file: {} (size: {} bytes)", pptFile.getName(), pptFile.length());

        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(pptFile))) {
            // Detect file format
            boolean isPptx = isPptxFormat(bis);
            
            // Create conversion context
            PptConversionContext context = new PptConversionContext(config, pptFile);
            
            if (isPptx) {
                return convertPptx(bis, context);
            } else {
                return convertPpt(bis, context);
            }
        } catch (Exception e) {
            logger.error("PowerPoint to Markdown conversion failed for file: " + pptFile.getName(), e);
            if (config.isStrictMode()) {
                throw new RuntimeException("Failed to convert PowerPoint document in STRICT mode", e);
            }
            // Fallback in loose mode
            return generateErrorFallback(pptFile, e);
        }
    }

    /**
     * Converts PPTX format presentation
     */
    private static String convertPptx(InputStream inputStream, PptConversionContext context) 
            throws IOException {
        
        logger.debug("Converting PPTX presentation");
        
        try (XMLSlideShow slideShow = new XMLSlideShow(inputStream)) {
            context.setSlideShow(slideShow);
            
            // Estimate output size for buffer pre-allocation
            int estimatedSize = estimateOutputSize(slideShow.getSlides().size());
            StringBuilder markdown = new StringBuilder(estimatedSize);
            
            // Add presentation metadata if enabled
            if (context.getConfig().isIncludeMetadata()) {
                addPresentationMetadata(markdown, slideShow, context);
            }
            
            // Convert slides
            convertSlides(slideShow.getSlides(), markdown, context);
            
            String result = markdown.toString();
            logger.debug("PPTX conversion completed (output length: {})", result.length());
            
            return result;
        }
    }

    /**
     * Converts PPT format presentation
     */
    private static String convertPpt(InputStream inputStream, PptConversionContext context) 
            throws IOException {
        
        logger.debug("Converting PPT presentation");
        
        try (HSLFSlideShow slideShow = new HSLFSlideShow(inputStream)) {
            context.setSlideShow(slideShow);
            
            // Estimate output size
            int estimatedSize = estimateOutputSize(slideShow.getSlides().size());
            StringBuilder markdown = new StringBuilder(estimatedSize);
            
            // Add presentation metadata if enabled
            if (context.getConfig().isIncludeMetadata()) {
                addPresentationMetadata(markdown, slideShow, context);
            }
            
            // Convert slides
            convertSlides(slideShow.getSlides(), markdown, context);
            
            String result = markdown.toString();
            logger.debug("PPT conversion completed (output length: {})", result.length());
            
            return result;
        }
    }

    /**
     * Converts slides to Markdown
     */
    private static void convertSlides(List<? extends Slide<?,?>> slides, StringBuilder markdown, 
                                    PptConversionContext context) {
        
        SlideConverter slideConverter = new SlideConverter();
        
        for (int i = 0; i < slides.size(); i++) {
            Slide<?,?> slide = slides.get(i);
            
            try {
                logger.debug("Converting slide {} of {}", i + 1, slides.size());
                
                // Add slide header
                markdown.append("## Slide ").append(i + 1).append("\n\n");
                
                // Convert slide content
                slideConverter.convertSlide(slide, markdown, context);
                
                // Add slide separator
                if (i < slides.size() - 1) {
                    markdown.append("\n---\n\n");
                }
                
            } catch (Exception e) {
                logger.warn("Failed to convert slide {}: {}", i + 1, e.getMessage());
                if (context.getConfig().isStrictMode()) {
                    throw new RuntimeException("Failed to convert slide " + (i + 1), e);
                }
                // In loose mode, add error placeholder
                markdown.append("*[Slide conversion failed: ").append(e.getMessage()).append("]*\n\n");
            }
        }
    }

    /**
     * Adds presentation metadata to markdown
     */
    private static void addPresentationMetadata(StringBuilder markdown, Object slideShow, 
                                              PptConversionContext context) {
        markdown.append("# Presentation Metadata\n\n");
        
        try {
            if (slideShow instanceof XMLSlideShow) {
                XMLSlideShow pptx = (XMLSlideShow) slideShow;
                addXMLSlideShowMetadata(markdown, pptx);
            } else if (slideShow instanceof HSLFSlideShow) {
                HSLFSlideShow ppt = (HSLFSlideShow) slideShow;
                addHSLFSlideShowMetadata(markdown, ppt);
            }
        } catch (Exception e) {
            logger.warn("Failed to extract presentation metadata", e);
            markdown.append("*Metadata extraction failed*\n");
        }
        
        markdown.append("\n---\n\n");
    }

    /**
     * Adds PPTX metadata
     */
    private static void addXMLSlideShowMetadata(StringBuilder markdown, XMLSlideShow slideShow) {
        try {
            var props = slideShow.getProperties();
            if (props != null && props.getCoreProperties() != null) {
                var core = props.getCoreProperties();
                
                if (core.getTitle() != null) {
                    markdown.append("**Title:** ").append(core.getTitle()).append("\n");
                }
                if (core.getCreator() != null) {
                    markdown.append("**Author:** ").append(core.getCreator()).append("\n");
                }
                if (core.getSubject() != null) {
                    markdown.append("**Subject:** ").append(core.getSubject()).append("\n");
                }
                if (core.getDescription() != null) {
                    markdown.append("**Description:** ").append(core.getDescription()).append("\n");
                }
            }
            
            markdown.append("**Slides:** ").append(slideShow.getSlides().size()).append("\n");
            
        } catch (Exception e) {
            logger.debug("Could not extract PPTX metadata", e);
        }
    }

    /**
     * Adds PPT metadata
     */
    private static void addHSLFSlideShowMetadata(StringBuilder markdown, HSLFSlideShow slideShow) {
        try {
            markdown.append("**Slides:** ").append(slideShow.getSlides().size()).append("\n");
            // PPT metadata extraction is more limited
        } catch (Exception e) {
            logger.debug("Could not extract PPT metadata", e);
        }
    }

    /**
     * Estimates output size for buffer pre-allocation
     */
    private static int estimateOutputSize(int slideCount) {
        // Rough estimate: 1KB per slide minimum
        return Math.max(2048, slideCount * 1024);
    }

    /**
     * Checks if file is a valid PowerPoint file
     */
    private static boolean isValidPptFile(File file) {
        if (file == null || !file.exists() || file.length() == 0) {
            return false;
        }
        
        String name = file.getName().toLowerCase();
        return name.endsWith(".ppt") || name.endsWith(".pptx") || name.endsWith(".pptm");
    }

    /**
     * Detects if the file is PPTX format (vs PPT)
     */
    private static boolean isPptxFormat(BufferedInputStream bis) throws IOException {
        bis.mark(4);
        byte[] header = new byte[4];
        int bytesRead = bis.read(header);
        bis.reset();
        
        if (bytesRead < 4) {
            return false;
        }
        
        // Check for ZIP signature (PPTX is ZIP-based)
        return Arrays.equals(header, PPTX_SIGNATURE);
    }

    /**
     * Generates error fallback content
     */
    private static String generateErrorFallback(File file, Exception e) {
        return String.format(
            "# PowerPoint Conversion Error\n\n" +
            "**File:** %s\n" +
            "**Error:** %s\n\n" +
            "*The PowerPoint presentation could not be converted to Markdown.*\n",
            file.getName(),
            e.getMessage()
        );
    }

    // Plugin interface implementation
    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return state;
    }

    @Override
    public void init(PluginContext context) throws PluginException {
        logger.info("Initializing PowerPoint to Markdown converter plugin");
        // Initialize plugin with context if needed
        state = PluginState.READY;
    }

    @Override
    public void start() throws PluginException {
        logger.info("Starting PowerPoint to Markdown converter plugin");
        state = PluginState.RUNNING;
    }

    @Override
    public void stop() throws PluginException {
        logger.info("Stopping PowerPoint to Markdown converter plugin");
        state = PluginState.STOPPED;
    }

    @Override
    public void destroy() {
        logger.info("Destroying PowerPoint to Markdown converter plugin");
        state = PluginState.DESTROYED;
    }

    // DocumentConverter interface implementation
    @Override
    public ConversionResult convert(File inputFile) throws ConversionException {
        try {
            String markdown = convertToMarkdown(inputFile, config);
            
            // Generate output path
            Path outputPath = Paths.get(inputFile.getParent(), 
                inputFile.getName().replaceAll("\\.(ppt|pptx|pptm)$", ".md"));
            
            return new ConversionResult(
                ConversionResult.Status.SUCCESS,
                inputFile.getPath(),
                outputPath.toString(),
                markdown
            );
            
        } catch (Exception e) {
            throw new ConversionException("Failed to convert PowerPoint file: " + inputFile.getName(), e);
        }
    }

    @Override
    public boolean supportsExtension(String extension) {
        if (extension == null) return false;
        String ext = extension.toLowerCase().startsWith(".") ? 
            extension.substring(1).toLowerCase() : extension.toLowerCase();
        return Arrays.asList(SUPPORTED_EXTENSIONS).contains(ext);
    }
}
