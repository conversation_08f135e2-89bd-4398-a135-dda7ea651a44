package com.talkweb.ai.indexer.util.rtf;

/**
 * Configuration class for RTF to Markdown conversion
 * 
 * This class provides various configuration options to control the behavior
 * of RTF document conversion, including parsing modes, formatting options,
 * and error handling strategies.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class RtfConversionConfig {

    private RtfConversionMode mode = RtfConversionMode.LOOSE;
    private boolean useAdvancedParsing = true;
    private boolean preserveFormatting = true;
    private boolean convertTables = true;
    private boolean convertImages = false; // RTF images are complex, disabled by default
    private boolean preserveLineBreaks = true;
    private boolean convertHyperlinks = true;
    private boolean includeMetadata = false;
    private int maxDocumentSize = 50 * 1024 * 1024; // 50MB limit
    private String defaultEncoding = "UTF-8";

    /**
     * Default constructor with standard settings
     */
    public RtfConversionConfig() {
        // Use default values
    }

    /**
     * Constructor with conversion mode
     */
    public RtfConversionConfig(RtfConversionMode mode) {
        this.mode = mode;
    }

    /**
     * Gets the conversion mode
     */
    public RtfConversionMode getMode() {
        return mode;
    }

    /**
     * Sets the conversion mode
     */
    public void setMode(RtfConversionMode mode) {
        this.mode = mode != null ? mode : RtfConversionMode.LOOSE;
    }

    /**
     * Checks if advanced parsing should be used
     */
    public boolean isUseAdvancedParsing() {
        return useAdvancedParsing;
    }

    /**
     * Sets whether to use advanced parsing
     */
    public void setUseAdvancedParsing(boolean useAdvancedParsing) {
        this.useAdvancedParsing = useAdvancedParsing;
    }

    /**
     * Checks if formatting should be preserved
     */
    public boolean isPreserveFormatting() {
        return preserveFormatting;
    }

    /**
     * Sets whether to preserve formatting
     */
    public void setPreserveFormatting(boolean preserveFormatting) {
        this.preserveFormatting = preserveFormatting;
    }

    /**
     * Checks if tables should be converted
     */
    public boolean isConvertTables() {
        return convertTables;
    }

    /**
     * Sets whether to convert tables
     */
    public void setConvertTables(boolean convertTables) {
        this.convertTables = convertTables;
    }

    /**
     * Checks if images should be converted
     */
    public boolean isConvertImages() {
        return convertImages;
    }

    /**
     * Sets whether to convert images
     */
    public void setConvertImages(boolean convertImages) {
        this.convertImages = convertImages;
    }

    /**
     * Checks if line breaks should be preserved
     */
    public boolean isPreserveLineBreaks() {
        return preserveLineBreaks;
    }

    /**
     * Sets whether to preserve line breaks
     */
    public void setPreserveLineBreaks(boolean preserveLineBreaks) {
        this.preserveLineBreaks = preserveLineBreaks;
    }

    /**
     * Checks if hyperlinks should be converted
     */
    public boolean isConvertHyperlinks() {
        return convertHyperlinks;
    }

    /**
     * Sets whether to convert hyperlinks
     */
    public void setConvertHyperlinks(boolean convertHyperlinks) {
        this.convertHyperlinks = convertHyperlinks;
    }

    /**
     * Checks if metadata should be included
     */
    public boolean isIncludeMetadata() {
        return includeMetadata;
    }

    /**
     * Sets whether to include metadata
     */
    public void setIncludeMetadata(boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
    }

    /**
     * Gets the maximum document size limit
     */
    public int getMaxDocumentSize() {
        return maxDocumentSize;
    }

    /**
     * Sets the maximum document size limit
     */
    public void setMaxDocumentSize(int maxDocumentSize) {
        this.maxDocumentSize = Math.max(1024, maxDocumentSize); // Minimum 1KB
    }

    /**
     * Gets the default encoding
     */
    public String getDefaultEncoding() {
        return defaultEncoding;
    }

    /**
     * Sets the default encoding
     */
    public void setDefaultEncoding(String defaultEncoding) {
        this.defaultEncoding = defaultEncoding != null ? defaultEncoding : "UTF-8";
    }

    /**
     * Creates a builder for fluent configuration
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for fluent configuration
     */
    public static class Builder {
        private final RtfConversionConfig config = new RtfConversionConfig();

        public Builder mode(RtfConversionMode mode) {
            config.setMode(mode);
            return this;
        }

        public Builder useAdvancedParsing(boolean useAdvancedParsing) {
            config.setUseAdvancedParsing(useAdvancedParsing);
            return this;
        }

        public Builder preserveFormatting(boolean preserveFormatting) {
            config.setPreserveFormatting(preserveFormatting);
            return this;
        }

        public Builder convertTables(boolean convertTables) {
            config.setConvertTables(convertTables);
            return this;
        }

        public Builder convertImages(boolean convertImages) {
            config.setConvertImages(convertImages);
            return this;
        }

        public Builder preserveLineBreaks(boolean preserveLineBreaks) {
            config.setPreserveLineBreaks(preserveLineBreaks);
            return this;
        }

        public Builder convertHyperlinks(boolean convertHyperlinks) {
            config.setConvertHyperlinks(convertHyperlinks);
            return this;
        }

        public Builder includeMetadata(boolean includeMetadata) {
            config.setIncludeMetadata(includeMetadata);
            return this;
        }

        public Builder maxDocumentSize(int maxDocumentSize) {
            config.setMaxDocumentSize(maxDocumentSize);
            return this;
        }

        public Builder defaultEncoding(String defaultEncoding) {
            config.setDefaultEncoding(defaultEncoding);
            return this;
        }

        public RtfConversionConfig build() {
            return config;
        }
    }

    @Override
    public String toString() {
        return "RtfConversionConfig{" +
                "mode=" + mode +
                ", useAdvancedParsing=" + useAdvancedParsing +
                ", preserveFormatting=" + preserveFormatting +
                ", convertTables=" + convertTables +
                ", convertImages=" + convertImages +
                ", preserveLineBreaks=" + preserveLineBreaks +
                ", convertHyperlinks=" + convertHyperlinks +
                ", includeMetadata=" + includeMetadata +
                ", maxDocumentSize=" + maxDocumentSize +
                ", defaultEncoding='" + defaultEncoding + '\'' +
                '}';
    }
}
