package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.DocumentConverter;
import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginContext;
import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.core.PluginState;
import com.talkweb.ai.indexer.util.odt.OdtConversionConfig;
import com.talkweb.ai.indexer.util.odt.OdtConversionContext;
import com.talkweb.ai.indexer.util.odt.OdtConversionMode;
import java.io.InputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import javax.xml.namespace.NamespaceContext;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.NamedNodeMap;

import java.io.File;
import java.util.logging.Logger;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * Enhanced ODT to Markdown converter with comprehensive compatibility and structure preservation
 *
 * Features:
 * - Full compatibility with ODT (OpenDocument Text) format documents
 * - Comprehensive content extraction (text, formatting, tables, lists)
 * - Structure preservation with proper Markdown hierarchy
 * - Configurable conversion options
 * - High-performance processing with memory optimization
 * - Robust error handling and recovery mechanisms
 * - Support for various ODT versions and features
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class OdtToMarkdownConverter implements DocumentConverter, Plugin {

    private static final Logger logger = Logger.getLogger(OdtToMarkdownConverter.class.getName());

    private final PluginMetadata metadata;
    private PluginState state = PluginState.STOPPED;
    private OdtConversionConfig config = new OdtConversionConfig();

    /**
     * Initializes the converter with the provided metadata.
     */
    public OdtToMarkdownConverter(PluginMetadata metadata) {
        this.metadata = metadata;
    }

    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return state;
    }

    @Override
    public void init(PluginContext context) throws PluginException {
        logger.info("Initializing ODT to Markdown converter");
        this.state = PluginState.INITIALIZING;
    }

    @Override
    public void start() throws PluginException {
        logger.info("Starting ODT to Markdown converter");
        this.state = PluginState.RUNNING;
    }

    @Override
    public void stop() throws PluginException {
        logger.info("Stopping ODT to Markdown converter");
        this.state = PluginState.STOPPED;
    }

    @Override
    public void destroy() {
        logger.info("Destroying ODT to Markdown converter");
        this.state = PluginState.DESTROYED;
    }

    @Override
    public ConversionResult convert(File input) throws ConversionException {
        if (!supportsExtension(getFileExtension(input.getName()))) {
            throw new IllegalArgumentException("Unsupported file type: " + input.getName());
        }

        logger.info("Converting ODT file: " + input.getAbsolutePath());
        
        try {
            String markdown = convertOdtToMarkdown(input);

            ConversionResult result = new ConversionResult(
                ConversionResult.Status.SUCCESS,
                markdown,
                "ODT conversion completed successfully"
            );

            logger.info("ODT conversion completed successfully");
            return result;

        } catch (Exception e) {
            logger.severe("ODT conversion failed: " + e.getMessage());

            if (config.getMode() == OdtConversionMode.STRICT) {
                throw new ConversionException("ODT conversion failed in STRICT mode", e);
            }

            // Return failed result in loose mode
            return new ConversionResult(
                ConversionResult.Status.FAILED,
                "",
                "ODT conversion failed: " + e.getMessage()
            );
        }
    }

    @Override
    public boolean supportsExtension(String extension) {
        if (extension == null) {
            return false;
        }
        String cleanExtension = extension.startsWith(".") ? extension.substring(1) : extension;
        return "odt".equalsIgnoreCase(cleanExtension);
    }

    /**
     * Converts ODT content to Markdown format using enhanced parser
     */
    private String convertOdtToMarkdown(File sourceFile) throws Exception {
        OdtConversionContext context = new OdtConversionContext(config.getMode(), config, sourceFile);

        try {
            String result;

            // Use enhanced parser if configured
            if (config.isUseAdvancedParsing()) {
                result = convertWithEnhancedParser(sourceFile, context);
            } else {
                result = convertWithBasicParser(sourceFile, context);
            }

            context.markCompleted();

            if (result.trim().isEmpty()) {
                result = "# Document Content\n\nThe ODT document appears to be empty or could not be processed.";
            }

            logger.info("ODT conversion completed. Length: " + result.length() + " characters");
            return result;

        } catch (Exception e) {
            context.addError("Failed to convert ODT document: " + e.getMessage());

            if (config.getMode() == OdtConversionMode.STRICT) {
                throw new Exception("ODT conversion failed in STRICT mode", e);
            }

            // Return error result in loose mode
            throw new Exception("ODT conversion failed in loose mode", e);
        }
    }

    /**
     * Converts ODT using enhanced parser with full feature support
     */
    private String convertWithEnhancedParser(File sourceFile, OdtConversionContext context) throws Exception {
        try (ZipFile zipFile = new ZipFile(sourceFile)) {
            EnhancedOdtParser parser = new EnhancedOdtParser(config, context);
            return parser.parseDocument(zipFile);
        } catch (Exception e) {
            logger.warning("Enhanced ODT parsing failed, falling back to basic parser: " + e.getMessage());

            if (config.getMode() == OdtConversionMode.STRICT) {
                throw e;
            }

            // Fallback to basic parser
            return convertWithBasicParser(sourceFile, context);
        }
    }

    /**
     * Converts ODT using basic parser (original implementation)
     */
    private String convertWithBasicParser(File sourceFile, OdtConversionContext context) throws Exception {
        StringBuilder markdown = new StringBuilder();

        try (ZipFile zipFile = new ZipFile(sourceFile)) {
            // Extract content.xml which contains the main document content
            ZipEntry contentEntry = zipFile.getEntry("content.xml");
            if (contentEntry != null) {
                try (InputStream contentStream = zipFile.getInputStream(contentEntry)) {
                    processOdtContent(contentStream, markdown, context);
                }
            } else {
                throw new Exception("content.xml not found in ODT file");
            }
        }

        return markdown.toString();
    }

    /**
     * Processes ODT content from XML stream
     */
    private void processOdtContent(InputStream contentStream, StringBuilder markdown, OdtConversionContext context) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(contentStream);

            // Get the root element and process content
            NodeList bodyNodes = document.getElementsByTagName("office:text");
            if (bodyNodes.getLength() > 0) {
                Node bodyNode = bodyNodes.item(0);
                processNode(bodyNode, markdown, context);
            } else {
                // Fallback: process all text content
                extractAllText(document.getDocumentElement(), markdown, context);
            }

        } catch (Exception e) {
            context.addError("Failed to process ODT content: " + e.getMessage());
            logger.warning("Error processing ODT content: " + e.getMessage());
        }
    }

    /**
     * Processes individual nodes in the document
     */
    private void processNode(Node node, StringBuilder markdown, OdtConversionContext context) {
        if (node == null) {
            return;
        }

        String nodeName = node.getNodeName();

        try {
            switch (nodeName) {
                case "text:h":
                    processHeading(node, markdown, context);
                    break;
                case "text:p":
                    processParagraph(node, markdown, context);
                    break;
                case "table:table":
                    if (config.isConvertTables()) {
                        processTable(node, markdown, context);
                    }
                    break;
                default:
                    // Process child nodes for other elements
                    NodeList children = node.getChildNodes();
                    for (int i = 0; i < children.getLength(); i++) {
                        processNode(children.item(i), markdown, context);
                    }
                    break;
            }
        } catch (Exception e) {
            context.addWarning("Failed to process node " + nodeName + ": " + e.getMessage());
            logger.warning("Error processing node " + nodeName + ": " + e.getMessage());
        }
    }

    /**
     * Extracts all text content from a node and its children
     */
    private void extractAllText(Node node, StringBuilder markdown, OdtConversionContext context) {
        if (node == null) {
            return;
        }

        if (node.getNodeType() == Node.TEXT_NODE) {
            String text = node.getTextContent();
            if (text != null && !text.trim().isEmpty()) {
                markdown.append(text.trim()).append(" ");
                context.incrementCharacterCount(text.length());
            }
        } else {
            NodeList children = node.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                extractAllText(children.item(i), markdown, context);
            }
        }
    }

    /**
     * Processes heading elements
     */
    private void processHeading(Node heading, StringBuilder markdown, OdtConversionContext context) {
        try {
            // Try to get outline level from attributes
            int headingLevel = 1;
            if (heading.hasAttributes()) {
                Node levelAttr = heading.getAttributes().getNamedItem("text:outline-level");
                if (levelAttr != null) {
                    try {
                        headingLevel = Integer.parseInt(levelAttr.getNodeValue());
                        headingLevel = Math.max(1, Math.min(6, headingLevel));
                    } catch (NumberFormatException e) {
                        // Use default level 1
                    }
                }
            }

            String headingText = heading.getTextContent();
            if (headingText != null && !headingText.trim().isEmpty()) {
                markdown.append("\n");
                for (int i = 0; i < headingLevel; i++) {
                    markdown.append("#");
                }
                markdown.append(" ").append(headingText.trim()).append("\n\n");
                context.incrementHeadingCount();
            }

        } catch (Exception e) {
            context.addWarning("Failed to process heading: " + e.getMessage());
        }
    }

    /**
     * Processes paragraph elements
     */
    private void processParagraph(Node paragraph, StringBuilder markdown, OdtConversionContext context) {
        try {
            String paragraphText = paragraph.getTextContent();
            if (paragraphText != null && !paragraphText.trim().isEmpty()) {
                markdown.append(paragraphText.trim()).append("\n\n");
                context.incrementParagraphCount();
                context.incrementCharacterCount(paragraphText.length());
            }

        } catch (Exception e) {
            context.addWarning("Failed to process paragraph: " + e.getMessage());
        }
    }

    /**
     * Processes table elements
     */
    private void processTable(Node table, StringBuilder markdown, OdtConversionContext context) {
        try {
            // Find table rows in the table node
            NodeList allChildren = table.getChildNodes();
            java.util.List<Node> rows = new java.util.ArrayList<>();

            for (int i = 0; i < allChildren.getLength(); i++) {
                Node child = allChildren.item(i);
                if ("table:table-row".equals(child.getNodeName())) {
                    rows.add(child);
                }
            }

            if (rows.isEmpty()) {
                return;
            }

            markdown.append("\n");

            // Process table rows
            for (int i = 0; i < rows.size(); i++) {
                processTableRow(rows.get(i), markdown, context, i == 0);
            }

            markdown.append("\n");
            context.incrementTableCount();

        } catch (Exception e) {
            context.addWarning("Failed to process table: " + e.getMessage());
        }
    }

    /**
     * Processes table row elements
     */
    private void processTableRow(Node row, StringBuilder markdown, OdtConversionContext context, boolean isHeader) {
        try {
            // Find table cells in the row node
            NodeList allChildren = row.getChildNodes();
            java.util.List<Node> cells = new java.util.ArrayList<>();

            for (int i = 0; i < allChildren.getLength(); i++) {
                Node child = allChildren.item(i);
                if ("table:table-cell".equals(child.getNodeName())) {
                    cells.add(child);
                }
            }

            // Process cells
            markdown.append("|");
            for (Node cell : cells) {
                String cellText = cell.getTextContent();
                markdown.append(" ").append(cellText != null ? cellText.trim() : "").append(" |");
            }
            markdown.append("\n");

            // Add header separator for first row
            if (isHeader && !cells.isEmpty()) {
                markdown.append("|");
                for (int i = 0; i < cells.size(); i++) {
                    markdown.append("---|");
                }
                markdown.append("\n");
            }

        } catch (Exception e) {
            context.addWarning("Failed to process table row: " + e.getMessage());
        }
    }

    /**
     * Gets file extension from filename
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }

    /**
     * Gets the current configuration
     */
    public OdtConversionConfig getConfig() {
        return config;
    }

    /**
     * Sets the conversion configuration
     */
    public void setConfig(OdtConversionConfig config) {
        this.config = config != null ? config : new OdtConversionConfig();
    }

    /**
     * Enhanced ODT Parser for comprehensive document processing
     */
    private static class EnhancedOdtParser {
        private final OdtConversionConfig config;
        private final OdtConversionContext context;
        private final Map<String, String> styles = new HashMap<>();
        private final Map<String, String> metadata = new HashMap<>();
        private final StringBuilder markdown = new StringBuilder();

        // Document structure tracking
        private int currentListLevel = 0;
        private boolean inList = false;
        private boolean inOrderedList = false;
        private int tableCount = 0;
        private int imageCount = 0;

        // Namespace context for ODT XML
        private final Map<String, String> namespaces = new HashMap<>();

        public EnhancedOdtParser(OdtConversionConfig config, OdtConversionContext context) {
            this.config = config;
            this.context = context;
            initializeNamespaces();
        }

        private void initializeNamespaces() {
            namespaces.put("office", "urn:oasis:names:tc:opendocument:xmlns:office:1.0");
            namespaces.put("text", "urn:oasis:names:tc:opendocument:xmlns:text:1.0");
            namespaces.put("table", "urn:oasis:names:tc:opendocument:xmlns:table:1.0");
            namespaces.put("draw", "urn:oasis:names:tc:opendocument:xmlns:drawing:1.0");
            namespaces.put("style", "urn:oasis:names:tc:opendocument:xmlns:style:1.0");
            namespaces.put("fo", "urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0");
            namespaces.put("meta", "urn:oasis:names:tc:opendocument:xmlns:meta:1.0");
            namespaces.put("dc", "http://purl.org/dc/elements/1.1/");
        }

        public String parseDocument(ZipFile zipFile) throws Exception {
            // Parse metadata first
            parseMetadata(zipFile);

            // Parse styles
            parseStyles(zipFile);

            // Parse main content
            parseContent(zipFile);

            // Add metadata to result if configured
            if (config.isIncludeMetadata() && !metadata.isEmpty()) {
                prependMetadata();
            }

            return markdown.toString();
        }

        private void parseMetadata(ZipFile zipFile) throws Exception {
            ZipEntry metaEntry = zipFile.getEntry("meta.xml");
            if (metaEntry != null) {
                try (InputStream metaStream = zipFile.getInputStream(metaEntry)) {
                    DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                    factory.setNamespaceAware(true);
                    DocumentBuilder builder = factory.newDocumentBuilder();
                    Document metaDoc = builder.parse(metaStream);

                    extractMetadataFromDocument(metaDoc);
                }
            }
        }

        private void extractMetadataFromDocument(Document metaDoc) {
            // Extract title
            NodeList titleNodes = metaDoc.getElementsByTagNameNS(namespaces.get("dc"), "title");
            if (titleNodes.getLength() > 0) {
                metadata.put("title", titleNodes.item(0).getTextContent());
            }

            // Extract author
            NodeList authorNodes = metaDoc.getElementsByTagNameNS(namespaces.get("dc"), "creator");
            if (authorNodes.getLength() > 0) {
                metadata.put("author", authorNodes.item(0).getTextContent());
            }

            // Extract subject
            NodeList subjectNodes = metaDoc.getElementsByTagNameNS(namespaces.get("dc"), "subject");
            if (subjectNodes.getLength() > 0) {
                metadata.put("subject", subjectNodes.item(0).getTextContent());
            }

            // Extract description
            NodeList descNodes = metaDoc.getElementsByTagNameNS(namespaces.get("dc"), "description");
            if (descNodes.getLength() > 0) {
                metadata.put("description", descNodes.item(0).getTextContent());
            }

            // Extract creation date
            NodeList dateNodes = metaDoc.getElementsByTagNameNS(namespaces.get("meta"), "creation-date");
            if (dateNodes.getLength() > 0) {
                metadata.put("creation-date", dateNodes.item(0).getTextContent());
            }
        }

        private void parseStyles(ZipFile zipFile) throws Exception {
            ZipEntry stylesEntry = zipFile.getEntry("styles.xml");
            if (stylesEntry != null) {
                try (InputStream stylesStream = zipFile.getInputStream(stylesEntry)) {
                    DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                    factory.setNamespaceAware(true);
                    DocumentBuilder builder = factory.newDocumentBuilder();
                    Document stylesDoc = builder.parse(stylesStream);

                    extractStylesFromDocument(stylesDoc);
                }
            }
        }

        private void extractStylesFromDocument(Document stylesDoc) {
            NodeList styleNodes = stylesDoc.getElementsByTagNameNS(namespaces.get("style"), "style");
            for (int i = 0; i < styleNodes.getLength(); i++) {
                Element styleElement = (Element) styleNodes.item(i);
                String styleName = styleElement.getAttribute("style:name");
                String styleFamily = styleElement.getAttribute("style:family");

                if (styleName != null && !styleName.isEmpty()) {
                    styles.put(styleName, styleFamily);
                }
            }
        }

        private void parseContent(ZipFile zipFile) throws Exception {
            ZipEntry contentEntry = zipFile.getEntry("content.xml");
            if (contentEntry == null) {
                throw new Exception("content.xml not found in ODT file");
            }

            try (InputStream contentStream = zipFile.getInputStream(contentEntry)) {
                DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                factory.setNamespaceAware(true);
                DocumentBuilder builder = factory.newDocumentBuilder();
                Document contentDoc = builder.parse(contentStream);

                // Process the document body
                NodeList bodyNodes = contentDoc.getElementsByTagNameNS(namespaces.get("office"), "text");
                if (bodyNodes.getLength() > 0) {
                    processContentNode(bodyNodes.item(0));
                }
            }
        }

        private void prependMetadata() {
            StringBuilder metaMarkdown = new StringBuilder();
            metaMarkdown.append("---\n");

            for (Map.Entry<String, String> entry : metadata.entrySet()) {
                metaMarkdown.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }

            metaMarkdown.append("---\n\n");
            markdown.insert(0, metaMarkdown.toString());
        }

        private void processContentNode(Node node) {
            if (node == null) return;

            String nodeName = node.getNodeName();

            switch (nodeName) {
                case "text:h":
                    processEnhancedHeading(node);
                    break;
                case "text:p":
                    processEnhancedParagraph(node);
                    break;
                case "text:list":
                    processEnhancedList(node);
                    break;
                case "table:table":
                    if (config.isConvertTables()) {
                        processEnhancedTable(node);
                    }
                    break;
                case "draw:frame":
                    processImage(node);
                    break;
                case "text:section":
                    processSection(node);
                    break;
                default:
                    // Process child nodes
                    NodeList children = node.getChildNodes();
                    for (int i = 0; i < children.getLength(); i++) {
                        processContentNode(children.item(i));
                    }
                    break;
            }
        }

        private void processEnhancedHeading(Node heading) {
            try {
                int headingLevel = 1;

                // Get outline level
                NamedNodeMap attributes = heading.getAttributes();
                if (attributes != null) {
                    Node levelAttr = attributes.getNamedItem("text:outline-level");
                    if (levelAttr != null) {
                        try {
                            headingLevel = Integer.parseInt(levelAttr.getNodeValue());
                            headingLevel = Math.max(1, Math.min(6, headingLevel));
                        } catch (NumberFormatException e) {
                            // Use default level 1
                        }
                    }
                }

                String headingText = extractTextWithFormatting(heading);
                if (headingText != null && !headingText.trim().isEmpty()) {
                    markdown.append("\n");
                    for (int i = 0; i < headingLevel; i++) {
                        markdown.append("#");
                    }
                    markdown.append(" ").append(headingText.trim()).append("\n\n");
                    context.incrementHeadingCount();
                }

            } catch (Exception e) {
                context.addWarning("Failed to process enhanced heading: " + e.getMessage());
            }
        }

        private void processEnhancedParagraph(Node paragraph) {
            try {
                String paragraphText = extractTextWithFormatting(paragraph);
                if (paragraphText != null && !paragraphText.trim().isEmpty()) {
                    // Check if this is a list item
                    if (inList) {
                        processListItem(paragraphText.trim());
                    } else {
                        markdown.append(paragraphText.trim()).append("\n\n");
                        context.incrementParagraphCount();
                        context.incrementCharacterCount(paragraphText.length());
                    }
                }

            } catch (Exception e) {
                context.addWarning("Failed to process enhanced paragraph: " + e.getMessage());
            }
        }

        private void processEnhancedList(Node list) {
            try {
                inList = true;
                currentListLevel++;

                // Determine if this is an ordered list
                NamedNodeMap attributes = list.getAttributes();
                if (attributes != null) {
                    Node styleAttr = attributes.getNamedItem("text:style-name");
                    if (styleAttr != null) {
                        String styleName = styleAttr.getNodeValue();
                        inOrderedList = isOrderedListStyle(styleName);
                    }
                }

                // Process list items
                NodeList children = list.getChildNodes();
                for (int i = 0; i < children.getLength(); i++) {
                    Node child = children.item(i);
                    if ("text:list-item".equals(child.getNodeName())) {
                        processListItemNode(child);
                    }
                }

                currentListLevel--;
                if (currentListLevel == 0) {
                    inList = false;
                    inOrderedList = false;
                    markdown.append("\n");
                }

            } catch (Exception e) {
                context.addWarning("Failed to process enhanced list: " + e.getMessage());
            }
        }

        private void processListItemNode(Node listItem) {
            NodeList children = listItem.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                Node child = children.item(i);
                if ("text:p".equals(child.getNodeName())) {
                    String itemText = extractTextWithFormatting(child);
                    if (itemText != null && !itemText.trim().isEmpty()) {
                        processListItem(itemText.trim());
                    }
                } else if ("text:list".equals(child.getNodeName())) {
                    // Nested list
                    processEnhancedList(child);
                }
            }
        }

        private void processListItem(String itemText) {
            // Add indentation for nested lists
            for (int i = 1; i < currentListLevel; i++) {
                markdown.append("  ");
            }

            // Add list marker
            if (inOrderedList) {
                markdown.append("1. ");
            } else {
                markdown.append("- ");
            }

            markdown.append(itemText).append("\n");
        }

        private boolean isOrderedListStyle(String styleName) {
            // Check if the style indicates an ordered list
            // This is a simplified check - in a real implementation,
            // we would parse the style definition
            return styleName != null &&
                   (styleName.toLowerCase().contains("number") ||
                    styleName.toLowerCase().contains("ordered"));
        }

        private void processEnhancedTable(Node table) {
            try {
                tableCount++;
                markdown.append("\n");

                // Process table rows
                NodeList children = table.getChildNodes();
                List<Node> rows = new ArrayList<>();

                for (int i = 0; i < children.getLength(); i++) {
                    Node child = children.item(i);
                    if ("table:table-row".equals(child.getNodeName())) {
                        rows.add(child);
                    }
                }

                // Process each row
                for (int i = 0; i < rows.size(); i++) {
                    processEnhancedTableRow(rows.get(i), i == 0);
                }

                markdown.append("\n");
                context.incrementTableCount();

            } catch (Exception e) {
                context.addWarning("Failed to process enhanced table: " + e.getMessage());
            }
        }

        private void processEnhancedTableRow(Node row, boolean isHeader) {
            try {
                List<String> cellContents = new ArrayList<>();

                NodeList children = row.getChildNodes();
                for (int i = 0; i < children.getLength(); i++) {
                    Node child = children.item(i);
                    if ("table:table-cell".equals(child.getNodeName())) {
                        String cellText = extractTextWithFormatting(child);
                        cellContents.add(cellText != null ? cellText.trim() : "");
                    }
                }

                // Output row
                if (!cellContents.isEmpty()) {
                    markdown.append("|");
                    for (String cell : cellContents) {
                        markdown.append(" ").append(cell).append(" |");
                    }
                    markdown.append("\n");

                    // Add header separator
                    if (isHeader) {
                        markdown.append("|");
                        for (int i = 0; i < cellContents.size(); i++) {
                            markdown.append("---|");
                        }
                        markdown.append("\n");
                    }
                }

            } catch (Exception e) {
                context.addWarning("Failed to process enhanced table row: " + e.getMessage());
            }
        }

        private void processImage(Node frame) {
            try {
                imageCount++;

                // Extract image information
                String imageName = "image" + imageCount;
                String imageTitle = "";

                NamedNodeMap attributes = frame.getAttributes();
                if (attributes != null) {
                    Node nameAttr = attributes.getNamedItem("draw:name");
                    if (nameAttr != null) {
                        imageName = nameAttr.getNodeValue();
                    }
                }

                // Look for image description
                NodeList children = frame.getChildNodes();
                for (int i = 0; i < children.getLength(); i++) {
                    Node child = children.item(i);
                    if ("svg:title".equals(child.getNodeName()) || "svg:desc".equals(child.getNodeName())) {
                        imageTitle = child.getTextContent();
                        break;
                    }
                }

                // Add image reference to markdown
                if (!imageTitle.isEmpty()) {
                    markdown.append("![").append(imageTitle).append("](").append(imageName).append(")\n\n");
                } else {
                    markdown.append("![").append(imageName).append("](").append(imageName).append(")\n\n");
                }

            } catch (Exception e) {
                context.addWarning("Failed to process image: " + e.getMessage());
            }
        }

        private void processSection(Node section) {
            try {
                // Extract section name if available
                NamedNodeMap attributes = section.getAttributes();
                if (attributes != null) {
                    Node nameAttr = attributes.getNamedItem("text:name");
                    if (nameAttr != null) {
                        String sectionName = nameAttr.getNodeValue();
                        markdown.append("\n<!-- Section: ").append(sectionName).append(" -->\n\n");
                    }
                }

                // Process section content
                NodeList children = section.getChildNodes();
                for (int i = 0; i < children.getLength(); i++) {
                    processContentNode(children.item(i));
                }

            } catch (Exception e) {
                context.addWarning("Failed to process section: " + e.getMessage());
            }
        }

        private String extractTextWithFormatting(Node node) {
            if (node == null) return "";

            StringBuilder text = new StringBuilder();
            extractTextWithFormattingRecursive(node, text);
            return text.toString();
        }

        private void extractTextWithFormattingRecursive(Node node, StringBuilder text) {
            if (node == null) return;

            String nodeName = node.getNodeName();

            switch (nodeName) {
                case "text:span":
                    processSpanFormatting(node, text);
                    break;
                case "text:a":
                    processHyperlink(node, text);
                    break;
                case "text:line-break":
                    text.append("\n");
                    break;
                case "text:tab":
                    text.append("\t");
                    break;
                case "text:s":
                    // Multiple spaces
                    NamedNodeMap attrs = node.getAttributes();
                    int spaceCount = 1;
                    if (attrs != null) {
                        Node cAttr = attrs.getNamedItem("text:c");
                        if (cAttr != null) {
                            try {
                                spaceCount = Integer.parseInt(cAttr.getNodeValue());
                            } catch (NumberFormatException e) {
                                // Use default
                            }
                        }
                    }
                    for (int i = 0; i < spaceCount; i++) {
                        text.append(" ");
                    }
                    break;
                default:
                    if (node.getNodeType() == Node.TEXT_NODE) {
                        text.append(node.getTextContent());
                    } else {
                        // Process child nodes
                        NodeList children = node.getChildNodes();
                        for (int i = 0; i < children.getLength(); i++) {
                            extractTextWithFormattingRecursive(children.item(i), text);
                        }
                    }
                    break;
            }
        }

        private void processSpanFormatting(Node span, StringBuilder text) {
            // Check for formatting attributes
            boolean isBold = false;
            boolean isItalic = false;
            boolean isUnderline = false;

            NamedNodeMap attributes = span.getAttributes();
            if (attributes != null) {
                Node styleAttr = attributes.getNamedItem("text:style-name");
                if (styleAttr != null) {
                    String styleName = styleAttr.getNodeValue();
                    // Check style for formatting (simplified)
                    if (styleName.toLowerCase().contains("bold")) {
                        isBold = true;
                    }
                    if (styleName.toLowerCase().contains("italic")) {
                        isItalic = true;
                    }
                    if (styleName.toLowerCase().contains("underline")) {
                        isUnderline = true;
                    }
                }
            }

            // Apply formatting
            if (isBold) text.append("**");
            if (isItalic) text.append("*");
            if (isUnderline) text.append("<u>");

            // Extract text content
            NodeList children = span.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                extractTextWithFormattingRecursive(children.item(i), text);
            }

            // Close formatting
            if (isUnderline) text.append("</u>");
            if (isItalic) text.append("*");
            if (isBold) text.append("**");
        }

        private void processHyperlink(Node link, StringBuilder text) {
            String href = "";
            NamedNodeMap attributes = link.getAttributes();
            if (attributes != null) {
                Node hrefAttr = attributes.getNamedItem("xlink:href");
                if (hrefAttr != null) {
                    href = hrefAttr.getNodeValue();
                }
            }

            // Extract text content directly without recursive formatting to avoid infinite recursion
            String linkText = link.getTextContent();

            if (!href.isEmpty() && !linkText.isEmpty()) {
                text.append("[").append(linkText.trim()).append("](").append(href).append(")");
            } else {
                text.append(linkText != null ? linkText.trim() : "");
            }
        }
    }
}
