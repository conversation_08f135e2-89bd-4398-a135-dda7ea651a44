package com.talkweb.ai.indexer.util.odt;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Context class for ODT to Markdown conversion
 * 
 * This class maintains the state and context information during
 * ODT document conversion, including formatting state, error tracking,
 * and conversion statistics.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class OdtConversionContext {

    private final OdtConversionMode mode;
    private final OdtConversionConfig config;
    private final File sourceFile;
    private final List<String> errors = new ArrayList<>();
    private final List<String> warnings = new ArrayList<>();
    private final Map<String, Object> metadata = new HashMap<>();
    private final Map<String, Object> conversionStats = new HashMap<>();

    // Document structure state
    private int currentHeadingLevel = 0;
    private boolean inParagraph = false;
    private boolean inTable = false;
    private boolean inList = false;
    private int listLevel = 0;
    private boolean inHyperlink = false;
    private boolean inFootnote = false;

    // Conversion statistics
    private long startTime;
    private long endTime;
    private int totalCharacters = 0;
    private int totalParagraphs = 0;
    private int totalHeadings = 0;
    private int totalTables = 0;
    private int totalLists = 0;
    private int totalImages = 0;
    private int totalHyperlinks = 0;
    private int totalFootnotes = 0;

    /**
     * Constructor
     */
    public OdtConversionContext(OdtConversionMode mode, OdtConversionConfig config, File sourceFile) {
        this.mode = mode != null ? mode : OdtConversionMode.getDefault();
        this.config = config != null ? config : new OdtConversionConfig();
        this.sourceFile = sourceFile;
        this.startTime = System.currentTimeMillis();
        
        initializeStats();
    }

    /**
     * Initializes conversion statistics
     */
    private void initializeStats() {
        conversionStats.put("startTime", startTime);
        conversionStats.put("totalCharacters", 0);
        conversionStats.put("totalParagraphs", 0);
        conversionStats.put("totalHeadings", 0);
        conversionStats.put("totalTables", 0);
        conversionStats.put("totalLists", 0);
        conversionStats.put("totalImages", 0);
        conversionStats.put("totalHyperlinks", 0);
        conversionStats.put("totalFootnotes", 0);
        conversionStats.put("errorCount", 0);
        conversionStats.put("warningCount", 0);
    }

    // Getters and setters for mode, config, and file
    public OdtConversionMode getMode() {
        return mode;
    }

    public OdtConversionConfig getConfig() {
        return config;
    }

    public File getSourceFile() {
        return sourceFile;
    }

    public boolean isStrictMode() {
        return mode == OdtConversionMode.STRICT;
    }

    public boolean isLooseMode() {
        return mode == OdtConversionMode.LOOSE;
    }

    // Error and warning management
    public void addError(String error) {
        errors.add(error);
        conversionStats.put("errorCount", errors.size());
    }

    public void addWarning(String warning) {
        warnings.add(warning);
        conversionStats.put("warningCount", warnings.size());
    }

    public List<String> getErrors() {
        return new ArrayList<>(errors);
    }

    public List<String> getWarnings() {
        return new ArrayList<>(warnings);
    }

    public boolean hasErrors() {
        return !errors.isEmpty();
    }

    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }

    // Document structure state management
    public int getCurrentHeadingLevel() {
        return currentHeadingLevel;
    }

    public void setCurrentHeadingLevel(int currentHeadingLevel) {
        this.currentHeadingLevel = Math.max(0, Math.min(6, currentHeadingLevel));
    }

    public boolean isInParagraph() {
        return inParagraph;
    }

    public void setInParagraph(boolean inParagraph) {
        this.inParagraph = inParagraph;
    }

    public boolean isInTable() {
        return inTable;
    }

    public void setInTable(boolean inTable) {
        this.inTable = inTable;
    }

    public boolean isInList() {
        return inList;
    }

    public void setInList(boolean inList) {
        this.inList = inList;
    }

    public int getListLevel() {
        return listLevel;
    }

    public void setListLevel(int listLevel) {
        this.listLevel = Math.max(0, listLevel);
    }

    public boolean isInHyperlink() {
        return inHyperlink;
    }

    public void setInHyperlink(boolean inHyperlink) {
        this.inHyperlink = inHyperlink;
    }

    public boolean isInFootnote() {
        return inFootnote;
    }

    public void setInFootnote(boolean inFootnote) {
        this.inFootnote = inFootnote;
    }

    // Statistics management
    public void incrementCharacterCount(int count) {
        totalCharacters += count;
        conversionStats.put("totalCharacters", totalCharacters);
    }

    public void incrementParagraphCount() {
        totalParagraphs++;
        conversionStats.put("totalParagraphs", totalParagraphs);
    }

    public void incrementHeadingCount() {
        totalHeadings++;
        conversionStats.put("totalHeadings", totalHeadings);
    }

    public void incrementTableCount() {
        totalTables++;
        conversionStats.put("totalTables", totalTables);
    }

    public void incrementListCount() {
        totalLists++;
        conversionStats.put("totalLists", totalLists);
    }

    public void incrementImageCount() {
        totalImages++;
        conversionStats.put("totalImages", totalImages);
    }

    public void incrementHyperlinkCount() {
        totalHyperlinks++;
        conversionStats.put("totalHyperlinks", totalHyperlinks);
    }

    public void incrementFootnoteCount() {
        totalFootnotes++;
        conversionStats.put("totalFootnotes", totalFootnotes);
    }

    // Metadata management
    public void setMetadata(String key, Object value) {
        metadata.put(key, value);
    }

    public Object getMetadata(String key) {
        return metadata.get(key);
    }

    public Map<String, Object> getAllMetadata() {
        return new HashMap<>(metadata);
    }

    // Conversion completion
    public void markCompleted() {
        endTime = System.currentTimeMillis();
        conversionStats.put("endTime", endTime);
        conversionStats.put("durationMs", endTime - startTime);
    }

    public Map<String, Object> getConversionStats() {
        return new HashMap<>(conversionStats);
    }

    public long getDurationMs() {
        long end = endTime > 0 ? endTime : System.currentTimeMillis();
        return end - startTime;
    }

    // Getters for statistics
    public int getTotalCharacters() {
        return totalCharacters;
    }

    public int getTotalParagraphs() {
        return totalParagraphs;
    }

    public int getTotalHeadings() {
        return totalHeadings;
    }

    public int getTotalTables() {
        return totalTables;
    }

    public int getTotalLists() {
        return totalLists;
    }

    public int getTotalImages() {
        return totalImages;
    }

    public int getTotalHyperlinks() {
        return totalHyperlinks;
    }

    public int getTotalFootnotes() {
        return totalFootnotes;
    }

    @Override
    public String toString() {
        return "OdtConversionContext{" +
                "mode=" + mode +
                ", sourceFile=" + (sourceFile != null ? sourceFile.getName() : "null") +
                ", errors=" + errors.size() +
                ", warnings=" + warnings.size() +
                ", totalCharacters=" + totalCharacters +
                ", totalParagraphs=" + totalParagraphs +
                ", totalHeadings=" + totalHeadings +
                ", totalTables=" + totalTables +
                ", durationMs=" + getDurationMs() +
                '}';
    }
}
