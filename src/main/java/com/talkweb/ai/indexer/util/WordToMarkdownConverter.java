package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import com.talkweb.ai.indexer.util.word.WordConversionContext;
import com.talkweb.ai.indexer.util.word.converter.*;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced Word to Markdown converter with comprehensive format support
 *
 * Features:
 * - Support for both .doc and .docx formats
 * - High-performance conversion with optimized processing
 * - Comprehensive Word element support (paragraphs, tables, images, lists)
 * - Configurable conversion options
 * - Detailed error handling and logging
 * - Image extraction and processing
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordToMarkdownConverter {

    private static final Logger logger = LoggerFactory.getLogger(WordToMarkdownConverter.class);
    
    // File format detection
    private static final byte[] DOC_SIGNATURE = {(byte) 0xD0, (byte) 0xCF, (byte) 0x11, (byte) 0xE0};
    private static final byte[] DOCX_SIGNATURE = {0x50, 0x4B, 0x03, 0x04}; // ZIP signature
    
    /**
     * Converts Word file to Markdown using default options
     * 
     * @param wordFile the Word file to convert
     * @return Markdown formatted string
     * @throws IOException if file reading fails
     */
    public static String convert(File wordFile) throws IOException {
        return convert(wordFile, WordConversionMode.LOOSE, new WordConversionOptions());
    }
    
    /**
     * Converts Word file to Markdown with specified mode
     * 
     * @param wordFile the Word file to convert
     * @param mode the conversion mode
     * @return Markdown formatted string
     * @throws IOException if file reading fails
     */
    public static String convert(File wordFile, WordConversionMode mode) throws IOException {
        return convert(wordFile, mode, new WordConversionOptions());
    }
    
    /**
     * Converts Word file to Markdown with specified options
     * 
     * @param wordFile the Word file to convert
     * @param options the conversion options
     * @return Markdown formatted string
     * @throws IOException if file reading fails
     */
    public static String convert(File wordFile, WordConversionOptions options) throws IOException {
        return convert(wordFile, WordConversionMode.LOOSE, options);
    }
    
    /**
     * Converts Word file to Markdown with full configuration
     * 
     * @param wordFile the Word file to convert
     * @param mode the conversion mode
     * @param options the conversion options
     * @return Markdown formatted string
     * @throws IOException if file reading fails
     */
    public static String convert(File wordFile, WordConversionMode mode, WordConversionOptions options) 
            throws IOException {
        
        if (wordFile == null || !wordFile.exists()) {
            throw new IllegalArgumentException("Word file does not exist: " + wordFile);
        }
        
        if (!wordFile.canRead()) {
            throw new IllegalArgumentException("Cannot read Word file: " + wordFile);
        }
        
        logger.info("Converting Word file to Markdown: {} (mode: {}, size: {} bytes)", 
                   wordFile.getName(), mode, wordFile.length());
        
        try (FileInputStream fis = new FileInputStream(wordFile)) {
            return convert(fis, wordFile, mode, options);
        }
    }
    
    /**
     * Converts Word file from InputStream
     * 
     * @param inputStream the input stream
     * @param sourceFile the source file (for context)
     * @param mode the conversion mode
     * @param options the conversion options
     * @return Markdown formatted string
     * @throws IOException if reading fails
     */
    public static String convert(InputStream inputStream, File sourceFile, 
                                WordConversionMode mode, WordConversionOptions options) 
            throws IOException {
        
        if (inputStream == null) {
            throw new IllegalArgumentException("InputStream cannot be null");
        }
        
        if (mode == null) {
            mode = WordConversionMode.LOOSE;
        }
        
        if (options == null) {
            options = new WordConversionOptions();
        }
        
        // Detect file format
        BufferedInputStream bis = new BufferedInputStream(inputStream);
        bis.mark(8);
        
        byte[] header = new byte[8];
        int bytesRead = bis.read(header);
        bis.reset();
        
        if (bytesRead < 4) {
            throw new IOException("Invalid file format - file too small");
        }
        
        boolean isDocx = isDocxFormat(header);
        boolean isDoc = isDocFormat(header);
        
        if (!isDocx && !isDoc) {
            throw new IOException("Unsupported file format - not a valid Word document");
        }
        
        // Create conversion context
        WordConversionContext context = new WordConversionContext(mode, options, sourceFile);
        
        try {
            if (isDocx) {
                return convertDocx(bis, context);
            } else {
                return convertDoc(bis, context);
            }
        } catch (Exception e) {
            logger.error("Word to Markdown conversion failed", e);
            if (mode == WordConversionMode.STRICT) {
                throw new RuntimeException("Failed to convert Word document in STRICT mode", e);
            }
            // Fallback in LOOSE mode
            return "# Document Conversion Failed\n\nThe document could not be converted to Markdown.";
        }
    }
    
    /**
     * Converts DOCX format document
     */
    private static String convertDocx(InputStream inputStream, WordConversionContext context) 
            throws IOException {
        
        logger.debug("Converting DOCX document");
        
        try (XWPFDocument document = new XWPFDocument(inputStream)) {
            context.setDocument(document);
            
            // Estimate output size for buffer pre-allocation
            int estimatedSize = estimateOutputSize(document);
            MarkdownBuilder builder = new MarkdownBuilder(estimatedSize);
            
            // Convert document elements
            convertDocxElements(document, builder, context);
            
            // Add footnotes if enabled
            if (context.getOptions().isConvertFootnotes() && !context.getFootnotes().isEmpty()) {
                appendFootnotes(builder, context);
            }
            
            String result = builder.toString();
            logger.debug("DOCX conversion completed (output length: {})", result.length());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Failed to convert DOCX document", e);
            throw new IOException("DOCX conversion failed", e);
        }
    }
    
    /**
     * Converts DOC format document
     */
    private static String convertDoc(InputStream inputStream, WordConversionContext context) 
            throws IOException {
        
        logger.debug("Converting DOC document");
        
        try (POIFSFileSystem fs = new POIFSFileSystem(inputStream);
             HWPFDocument document = new HWPFDocument(fs)) {
            
            context.setDocument(document);
            
            // Estimate output size
            int estimatedSize = Math.max(1024, document.getRange().text().length());
            MarkdownBuilder builder = new MarkdownBuilder(estimatedSize);
            
            // Convert document elements
            convertDocElements(document, builder, context);
            
            String result = builder.toString();
            logger.debug("DOC conversion completed (output length: {})", result.length());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Failed to convert DOC document", e);
            throw new IOException("DOC conversion failed", e);
        }
    }
    
    /**
     * Converts DOCX document elements
     */
    private static void convertDocxElements(XWPFDocument document, MarkdownBuilder builder, 
                                          WordConversionContext context) {
        
        // Initialize converters
        List<WordElementConverter<Object>> converters = createConverters();
        
        // Convert body elements
        for (IBodyElement element : document.getBodyElements()) {
            convertElement(element, builder, context, converters);
        }
    }
    
    /**
     * Converts DOC document elements
     */
    private static void convertDocElements(HWPFDocument document, MarkdownBuilder builder, 
                                         WordConversionContext context) {
        
        Range range = document.getRange();
        
        // For DOC files, we have limited structure information
        // Convert as paragraphs for now
        WordParagraphConverter paragraphConverter = new WordParagraphConverter();
        
        try {
            // This is a simplified conversion for DOC files
            // A full implementation would need to parse the document structure more carefully
            String text = range.text();
            String[] paragraphs = text.split("\r");
            
            for (String paragraph : paragraphs) {
                if (paragraph.trim().isEmpty()) {
                    continue;
                }
                
                builder.newline().append(paragraph.trim()).newline().newline();
            }
            
        } catch (Exception e) {
            logger.warn("Failed to convert DOC elements", e);
            if (context.isStrictMode()) {
                throw new RuntimeException("DOC conversion failed", e);
            }
        }
    }
    
    /**
     * Converts a single element using appropriate converter
     */
    private static void convertElement(Object element, MarkdownBuilder builder, 
                                     WordConversionContext context, 
                                     List<WordElementConverter<Object>> converters) {
        
        for (WordElementConverter<Object> converter : converters) {
            if (converter.canConvert(element, context)) {
                try {
                    converter.beforeConvert(element, builder, context);
                    converter.convert(element, builder, context);
                    converter.afterConvert(element, builder, context);
                    return;
                } catch (WordElementConverter.ConversionException e) {
                    logger.warn("Converter {} failed for element {}", 
                               converter.getClass().getSimpleName(), element.getClass().getSimpleName(), e);
                    if (context.isStrictMode()) {
                        throw new RuntimeException("Element conversion failed", e);
                    }
                    // Continue to next converter in loose mode
                }
            }
        }
        
        // No converter found
        logger.debug("No converter found for element: {}", element.getClass().getSimpleName());
    }
    
    /**
     * Creates list of converters in priority order
     */
    @SuppressWarnings("unchecked")
    private static List<WordElementConverter<Object>> createConverters() {
        List<WordElementConverter<Object>> converters = new ArrayList<>();
        
        // Add converters in priority order
        converters.add((WordElementConverter<Object>) new WordTableConverter());
        converters.add((WordElementConverter<Object>) new WordParagraphConverter());
        
        return converters;
    }
    
    /**
     * Estimates output size for buffer pre-allocation
     */
    private static int estimateOutputSize(XWPFDocument document) {
        // Rough estimate based on document content
        int textLength = 0;
        
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text != null) {
                textLength += text.length();
            }
        }
        
        // Markdown is usually 80-120% of original text size
        return Math.max(1024, (int) (textLength * 1.2));
    }
    
    /**
     * Appends footnotes to the markdown
     */
    private static void appendFootnotes(MarkdownBuilder builder, WordConversionContext context) {
        List<String> footnotes = context.getFootnotes();
        if (footnotes.isEmpty()) {
            return;
        }
        
        builder.newline().newline().append("---").newline().newline();
        builder.append("## Footnotes").newline().newline();

        for (int i = 0; i < footnotes.size(); i++) {
            builder.append(String.valueOf(i + 1)).append(". ").append(footnotes.get(i)).newline();
        }
    }
    
    /**
     * Checks if file format is DOCX
     */
    private static boolean isDocxFormat(byte[] header) {
        if (header.length < 4) return false;
        
        // Check for ZIP signature (DOCX is a ZIP file)
        return header[0] == DOCX_SIGNATURE[0] && 
               header[1] == DOCX_SIGNATURE[1] && 
               header[2] == DOCX_SIGNATURE[2] && 
               header[3] == DOCX_SIGNATURE[3];
    }
    
    /**
     * Checks if file format is DOC
     */
    private static boolean isDocFormat(byte[] header) {
        if (header.length < 4) return false;
        
        // Check for OLE2 signature
        return header[0] == DOC_SIGNATURE[0] && 
               header[1] == DOC_SIGNATURE[1] && 
               header[2] == DOC_SIGNATURE[2] && 
               header[3] == DOC_SIGNATURE[3];
    }
}
