
package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.util.markdown.*;
import com.talkweb.ai.indexer.util.markdown.converter.*;
import org.jsoup.Jsoup;
import org.jsoup.nodes.*;
import org.jsoup.select.Elements;
import org.jsoup.safety.Safelist;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Enhanced HTML to Markdown converter with optimized performance, extensibility, and readability
 *
 * Features:
 * - High-performance conversion with caching
 * - Extensible converter architecture
 * - Comprehensive HTML element support
 * - Configurable conversion options
 * - Detailed error handling and logging
 *
 * <AUTHOR> Assistant
 * @version 2.0
 */
public class HtmlToMarkdownConverter {

    private static final Logger logger = LoggerFactory.getLogger(HtmlToMarkdownConverter.class);
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");
    private static final Pattern NEWLINE_PATTERN = Pattern.compile("\n{3,}");

    // Singleton converter registry for performance
    private static final ConverterRegistry DEFAULT_REGISTRY = new ConverterRegistry();

    /**
     * Gets cache statistics from the default registry
     * @return map of cache statistics
     */
    public static Map<String, Object> getCacheStatistics() {
        return DEFAULT_REGISTRY.getCacheStats();
    }

    /**
     * Clears the converter cache
     */
    public static void clearCache() {
        DEFAULT_REGISTRY.clearCache();
    }

    /**
     * Converts HTML string to Markdown format using default LOOSE mode.
     * @param html HTML content to convert
     * @return Markdown formatted string
     */
    public static String convert(String html) {
        return convert(html, HtmlConversionMode.LOOSE);
    }

    /**
     * Converts HTML string to Markdown format
     * @param html HTML content to convert
     * @param mode Conversion mode (STRICT or LOOSE)
     * @return Markdown formatted string
     */
    public static String convert(String html, HtmlConversionMode mode) {
        return convert(html, mode, DEFAULT_REGISTRY);
    }

    /**
     * Converts HTML string to Markdown format with custom converter registry
     * @param html HTML content to convert
     * @param mode Conversion mode (STRICT or LOOSE)
     * @param registry Custom converter registry
     * @return Markdown formatted string
     */
    public static String convert(String html, HtmlConversionMode mode, ConverterRegistry registry) {
        try {
            if (html == null || html.isEmpty()) {
                return "";
            }

            logger.debug("Converting HTML to Markdown (mode: {}, length: {})", mode, html.length());

            // Parse HTML with JSoup for proper DOM handling
            Document doc = Jsoup.parse(html);

            if (mode == HtmlConversionMode.STRICT) {
                validateHtmlForStrictMode(doc, html);
            }

            // Create conversion context
            ConversionContext context = new ConversionContext(mode);

            // Create optimized markdown builder
            MarkdownBuilder builder = new MarkdownBuilder(estimateOutputSize(html));

            // Convert using the new architecture
            Element rootElement = doc.body() != null ? doc.body() : doc;
            convertElementWithRegistry(rootElement, builder, context, registry);

            // Clean up and return result
            String result = builder.normalizeWhitespace().toString();

            logger.debug("Conversion completed (output length: {})", result.length());
            return result;

        } catch (Exception e) {
            logger.error("HTML to Markdown conversion failed", e);
            if (mode == HtmlConversionMode.STRICT) {
                throw new RuntimeException("Failed to convert HTML in STRICT mode", e);
            }
            // Fallback to basic conversion in LOOSE mode
            return fallbackConversion(html);
        }
    }

    /**
     * Estimates the output size for buffer pre-allocation
     * @param html the input HTML
     * @return estimated output size
     */
    private static int estimateOutputSize(String html) {
        if (html == null) return 0;
        // Rough estimate: markdown is usually 70-90% of HTML size
        // For large files, use a more conservative estimate to avoid excessive memory allocation
        int baseSize = Math.max(1024, (int) (html.length() * 0.8));

        // Cap the initial buffer size for very large files to prevent memory issues
        return Math.min(baseSize, 1024 * 1024); // Max 1MB initial buffer
    }

    /**
     * Converts large HTML content with streaming approach for better memory efficiency
     * @param html HTML content to convert
     * @param mode Conversion mode (STRICT or LOOSE)
     * @return Markdown formatted string
     */
    public static String convertLarge(String html, HtmlConversionMode mode) {
        if (html == null || html.length() < 100_000) {
            // Use regular conversion for smaller files
            return convert(html, mode);
        }

        logger.debug("Using streaming conversion for large HTML (length: {})", html.length());

        // For very large files, process in chunks to reduce memory pressure
        try {
            Document doc = Jsoup.parse(html);

            if (mode == HtmlConversionMode.STRICT) {
                validateHtmlForStrictMode(doc, html);
            }

            ConversionContext context = new ConversionContext(mode);
            MarkdownBuilder builder = new MarkdownBuilder(estimateOutputSize(html));

            Element rootElement = doc.body() != null ? doc.body() : doc;

            // Process elements in smaller batches to reduce memory pressure
            processElementsInBatches(rootElement, builder, context, DEFAULT_REGISTRY);

            String result = builder.normalizeWhitespace().toString();
            logger.debug("Large file conversion completed (output length: {})", result.length());
            return result;

        } catch (Exception e) {
            logger.error("Large HTML to Markdown conversion failed", e);
            if (mode == HtmlConversionMode.STRICT) {
                throw new RuntimeException("Failed to convert large HTML in STRICT mode", e);
            }
            return fallbackConversion(html);
        }
    }

    /**
     * Processes elements in batches to reduce memory pressure
     */
    private static void processElementsInBatches(Element rootElement, MarkdownBuilder builder,
                                               ConversionContext context, ConverterRegistry registry) {
        // Process direct children in batches
        Elements children = rootElement.children();
        int batchSize = Math.max(10, Math.min(100, children.size() / 10));

        for (int i = 0; i < children.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, children.size());

            for (int j = i; j < endIndex; j++) {
                Element child = children.get(j);
                try {
                    registry.convert(child, builder, context);
                } catch (ElementConverter.ConversionException e) {
                    logger.warn("Failed to convert element in batch: {}", child.tagName(), e);
                }
            }

            // Suggest garbage collection after each batch for large files
            if (children.size() > 1000 && (i + batchSize) % 500 == 0) {
                System.gc();
            }
        }
    }

    /**
     * Converts an element using the converter registry
     * @param element the HTML element
     * @param builder the markdown builder
     * @param context the conversion context
     * @param registry the converter registry
     */
    private static void convertElementWithRegistry(Element element, MarkdownBuilder builder,
                                                  ConversionContext context, ConverterRegistry registry) {
        try {
            registry.convert(element, builder, context);
        } catch (ElementConverter.ConversionException e) {
            logger.warn("Failed to convert element: {}", element.tagName(), e);
            // Fallback to processing children
            for (Element child : element.children()) {
                convertElementWithRegistry(child, builder, context, registry);
            }
        }
    }

    /**
     * Validates HTML for strict mode compliance
     */
    private static void validateHtmlForStrictMode(Document doc, String originalHtml) {
        // Check for potentially problematic elements
        Elements scripts = doc.select("script");
        Elements styles = doc.select("style");

        if (!scripts.isEmpty() || !styles.isEmpty()) {
            throw new IllegalArgumentException("HTML contains script or style tags in STRICT mode.");
        }

        // Additional validation can be added here
        Document.OutputSettings settings = new Document.OutputSettings().prettyPrint(false);
        String cleanedHtml = Jsoup.clean(originalHtml, "", Safelist.relaxed(), settings);

        // Check if cleaning removed significant content (indicating problematic HTML)
        if (cleanedHtml.length() < originalHtml.length() * 0.8) {
            throw new IllegalArgumentException("HTML contains too many unsupported elements in STRICT mode.");
        }
    }

    /**
     * Recursively converts HTML elements to Markdown while preserving order
     */
    private static void convertElement(Element element, StringBuilder markdown, int depth) {
        if (element == null) return;

        String tagName = element.tagName().toLowerCase();

        // Handle different element types
        switch (tagName) {
            case "h1":
            case "h2":
            case "h3":
            case "h4":
            case "h5":
            case "h6":
                convertHeading(element, markdown, tagName);
                break;
            case "p":
                convertParagraph(element, markdown, depth);
                break;
            case "a":
                convertLink(element, markdown, depth);
                break;
            case "img":
                convertImage(element, markdown);
                break;
            case "strong":
            case "b":
                convertBold(element, markdown, depth);
                break;
            case "em":
            case "i":
                convertItalic(element, markdown, depth);
                break;
            case "code":
                convertInlineCode(element, markdown);
                break;
            case "pre":
                convertCodeBlock(element, markdown);
                break;
            case "blockquote":
                convertBlockquote(element, markdown, depth);
                break;
            case "ul":
            case "ol":
                convertList(element, markdown, depth, tagName.equals("ol"));
                break;
            case "li":
                // List items are handled by their parent ul/ol
                convertListItem(element, markdown, depth);
                break;
            case "table":
                convertTable(element, markdown);
                break;
            case "hr":
                markdown.append("\n---\n\n");
                break;
            case "br":
                markdown.append("  \n");
                break;
            case "div":
            case "span":
            case "section":
            case "article":
            case "header":
            case "footer":
            case "main":
            case "aside":
            case "nav":
                // Generic containers - just process children
                convertChildren(element, markdown, depth);
                break;
            default:
                // For unknown elements, just process children
                convertChildren(element, markdown, depth);
                break;
        }
    }

    /**
     * Converts heading elements to Markdown
     */
    private static void convertHeading(Element element, StringBuilder markdown, String tagName) {
        int level = Integer.parseInt(tagName.substring(1));
        markdown.append("\n");
        for (int i = 0; i < level; i++) {
            markdown.append("#");
        }
        markdown.append(" ");
        appendTextContent(element, markdown);
        markdown.append("\n\n");
    }

    /**
     * Converts paragraph elements to Markdown
     */
    private static void convertParagraph(Element element, StringBuilder markdown, int depth) {
        markdown.append("\n");
        convertChildren(element, markdown, depth);
        markdown.append("\n\n");
    }

    /**
     * Converts link elements to Markdown
     */
    private static void convertLink(Element element, StringBuilder markdown, int depth) {
        String href = element.attr("href");
        String title = element.attr("title");

        markdown.append("[");
        appendTextContent(element, markdown);
        markdown.append("](").append(href);

        if (!title.isEmpty()) {
            markdown.append(" \"").append(title).append("\"");
        }

        markdown.append(")");
    }

    /**
     * Converts image elements to Markdown
     */
    private static void convertImage(Element element, StringBuilder markdown) {
        String src = element.attr("src");
        String alt = element.attr("alt");
        String title = element.attr("title");

        markdown.append("![").append(alt).append("](").append(src);

        if (!title.isEmpty()) {
            markdown.append(" \"").append(title).append("\"");
        }

        markdown.append(")");
    }

    /**
     * Converts bold elements to Markdown
     */
    private static void convertBold(Element element, StringBuilder markdown, int depth) {
        markdown.append("**");
        convertChildren(element, markdown, depth);
        markdown.append("**");
    }

    /**
     * Converts italic elements to Markdown
     */
    private static void convertItalic(Element element, StringBuilder markdown, int depth) {
        markdown.append("*");
        convertChildren(element, markdown, depth);
        markdown.append("*");
    }

    /**
     * Converts inline code elements to Markdown
     */
    private static void convertInlineCode(Element element, StringBuilder markdown) {
        markdown.append("`");
        appendTextContent(element, markdown);
        markdown.append("`");
    }

    /**
     * Converts code block elements to Markdown
     */
    private static void convertCodeBlock(Element element, StringBuilder markdown) {
        markdown.append("\n```\n");
        appendTextContent(element, markdown);
        markdown.append("\n```\n\n");
    }

    /**
     * Converts blockquote elements to Markdown
     */
    private static void convertBlockquote(Element element, StringBuilder markdown, int depth) {
        markdown.append("\n");
        String content = getTextContent(element);
        String[] lines = content.split("\n");

        for (String line : lines) {
            if (!line.trim().isEmpty()) {
                markdown.append("> ").append(line.trim()).append("\n");
            }
        }
        markdown.append("\n");
    }

    /**
     * Converts list elements to Markdown
     */
    private static void convertList(Element element, StringBuilder markdown, int depth, boolean ordered) {
        markdown.append("\n");
        Elements listItems = element.children().select("li");

        for (int i = 0; i < listItems.size(); i++) {
            Element li = listItems.get(i);

            // Add indentation for nested lists
            for (int j = 0; j < depth; j++) {
                markdown.append("  ");
            }

            if (ordered) {
                markdown.append((i + 1)).append(". ");
            } else {
                markdown.append("- ");
            }

            convertChildren(li, markdown, depth + 1);
            markdown.append("\n");
        }
        markdown.append("\n");
    }

    /**
     * Converts list item elements to Markdown
     */
    private static void convertListItem(Element element, StringBuilder markdown, int depth) {
        // List items are handled by their parent list
        convertChildren(element, markdown, depth);
    }

    /**
     * Converts table elements to Markdown
     */
    private static void convertTable(Element element, StringBuilder markdown) {
        markdown.append("\n");

        Elements rows = element.select("tr");
        if (rows.isEmpty()) {
            return;
        }

        List<List<String>> tableData = new ArrayList<>();
        int maxCols = 0;

        // Extract table data
        for (Element row : rows) {
            List<String> rowData = new ArrayList<>();
            Elements cells = row.select("td, th");

            for (Element cell : cells) {
                String cellContent = getTextContent(cell).trim();
                // Escape pipe characters in cell content
                cellContent = cellContent.replace("|", "\\|");
                rowData.add(cellContent);
            }

            tableData.add(rowData);
            maxCols = Math.max(maxCols, rowData.size());
        }

        if (tableData.isEmpty() || maxCols == 0) {
            return;
        }

        // Normalize table data (ensure all rows have same number of columns)
        for (List<String> row : tableData) {
            while (row.size() < maxCols) {
                row.add("");
            }
        }

        // Write table header
        List<String> headerRow = tableData.get(0);
        markdown.append("|");
        for (String cell : headerRow) {
            markdown.append(" ").append(cell).append(" |");
        }
        markdown.append("\n");

        // Write separator row
        markdown.append("|");
        for (int i = 0; i < maxCols; i++) {
            markdown.append("---|");
        }
        markdown.append("\n");

        // Write data rows
        for (int i = 1; i < tableData.size(); i++) {
            List<String> row = tableData.get(i);
            markdown.append("|");
            for (String cell : row) {
                markdown.append(" ").append(cell).append(" |");
            }
            markdown.append("\n");
        }

        markdown.append("\n");
    }

    /**
     * Processes child elements recursively
     */
    private static void convertChildren(Element element, StringBuilder markdown, int depth) {
        for (Node child : element.childNodes()) {
            if (child instanceof Element) {
                convertElement((Element) child, markdown, depth);
            } else if (child instanceof TextNode) {
                String text = ((TextNode) child).text();
                if (!text.trim().isEmpty()) {
                    markdown.append(text);
                }
            }
        }
    }

    /**
     * Extracts text content from an element, preserving some formatting
     */
    private static String getTextContent(Element element) {
        StringBuilder text = new StringBuilder();
        extractTextRecursively(element, text);
        return text.toString();
    }

    /**
     * Recursively extracts text from element and its children
     */
    private static void extractTextRecursively(Element element, StringBuilder text) {
        for (Node child : element.childNodes()) {
            if (child instanceof TextNode) {
                String nodeText = ((TextNode) child).text();
                if (!nodeText.trim().isEmpty()) {
                    text.append(nodeText);
                }
            } else if (child instanceof Element) {
                Element childElement = (Element) child;
                String tagName = childElement.tagName().toLowerCase();

                // Add line breaks for block elements
                if (isBlockElement(tagName)) {
                    text.append("\n");
                }

                extractTextRecursively(childElement, text);

                if (isBlockElement(tagName)) {
                    text.append("\n");
                }
            }
        }
    }

    /**
     * Appends text content to markdown, handling inline formatting
     */
    private static void appendTextContent(Element element, StringBuilder markdown) {
        String text = getTextContent(element);
        // Normalize whitespace but preserve intentional line breaks
        text = WHITESPACE_PATTERN.matcher(text).replaceAll(" ");
        markdown.append(text.trim());
    }

    /**
     * Checks if an element is a block-level element
     */
    private static boolean isBlockElement(String tagName) {
        return tagName.matches("^(div|p|h[1-6]|ul|ol|li|table|tr|td|th|thead|tbody|tfoot|blockquote|pre|hr|section|article|header|footer|main|aside|nav)$");
    }

    /**
     * Cleans up excessive whitespace in the final markdown
     */
    private static String cleanupWhitespace(String markdown) {
        // Remove excessive newlines
        markdown = NEWLINE_PATTERN.matcher(markdown).replaceAll("\n\n");

        // Remove trailing whitespace from lines
        String[] lines = markdown.split("\n");
        StringBuilder cleaned = new StringBuilder();

        for (String line : lines) {
            cleaned.append(line.replaceAll("\\s+$", "")).append("\n");
        }

        return cleaned.toString();
    }

    /**
     * Fallback conversion using simple regex replacement for LOOSE mode
     */
    private static String fallbackConversion(String html) {
        String markdown = html;

        // Decode HTML entities
        markdown = decodeHtmlEntities(markdown);

        // Convert headers
        markdown = markdown.replaceAll("(?i)<h1[^>]*>(.*?)</h1>", "# $1\n\n")
                          .replaceAll("(?i)<h2[^>]*>(.*?)</h2>", "## $1\n\n")
                          .replaceAll("(?i)<h3[^>]*>(.*?)</h3>", "### $1\n\n")
                          .replaceAll("(?i)<h4[^>]*>(.*?)</h4>", "#### $1\n\n")
                          .replaceAll("(?i)<h5[^>]*>(.*?)</h5>", "##### $1\n\n")
                          .replaceAll("(?i)<h6[^>]*>(.*?)</h6>", "###### $1\n\n");

        // Convert paragraphs
        markdown = markdown.replaceAll("(?i)<p[^>]*>(.*?)</p>", "$1\n\n");

        // Convert links
        markdown = markdown.replaceAll("(?i)<a[^>]*href=[\"'](.*?)[\"'][^>]*>(.*?)</a>", "[$2]($1)");

        // Convert text formatting
        markdown = markdown.replaceAll("(?i)<(strong|b)[^>]*>(.*?)</\\1>", "**$2**")
                          .replaceAll("(?i)<(em|i)[^>]*>(.*?)</\\1>", "*$2*");

        // Convert lists
        markdown = markdown.replaceAll("(?i)<li[^>]*>(.*?)</li>", "- $1\n")
                          .replaceAll("(?i)</?[uo]l[^>]*>", "");

        // Convert code
        markdown = markdown.replaceAll("(?i)<code[^>]*>(.*?)</code>", "`$1`")
                          .replaceAll("(?i)<pre[^>]*>(.*?)</pre>", "```\n$1\n```");

        // Convert blockquotes
        markdown = markdown.replaceAll("(?i)<blockquote[^>]*>(.*?)</blockquote>", "> $1\n\n");

        // Convert horizontal rules
        markdown = markdown.replaceAll("(?i)<hr[^>]*>", "---\n");

        // Convert images
        markdown = markdown.replaceAll("(?i)<img[^>]*src=[\"'](.*?)[\"'][^>]*alt=[\"'](.*?)[\"'][^>]*>", "![$2]($1)")
                          .replaceAll("(?i)<img[^>]*alt=[\"'](.*?)[\"'][^>]*src=[\"'](.*?)[\"'][^>]*>", "![$1]($2)")
                          .replaceAll("(?i)<img[^>]*src=[\"'](.*?)[\"'][^>]*>", "![]($1)");

        // Remove remaining HTML tags
        markdown = markdown.replaceAll("<[^>]+>", "");

        // Clean up whitespace
        return cleanupWhitespace(markdown);
    }

    /**
     * Decodes common HTML entities
     */
    private static String decodeHtmlEntities(String text) {
        return text.replace("&lt;", "<")
                  .replace("&gt;", ">")
                  .replace("&amp;", "&")
                  .replace("&quot;", "\"")
                  .replace("&apos;", "'")
                  .replace("&nbsp;", " ")
                  .replace("&copy;", "©")
                  .replace("&reg;", "®")
                  .replace("&trade;", "™")
                  .replace("&hellip;", "…")
                  .replace("&mdash;", "—")
                  .replace("&ndash;", "–")
                  .replace("&lsquo;", "'")
                  .replace("&rsquo;", "'")
                  .replace("&ldquo;", "\"")
                  .replace("&rdquo;", "\"");
    }
}
