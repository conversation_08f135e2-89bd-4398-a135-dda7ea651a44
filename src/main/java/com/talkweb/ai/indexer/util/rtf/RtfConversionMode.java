package com.talkweb.ai.indexer.util.rtf;

/**
 * Enumeration of RTF conversion modes
 * 
 * This enum defines different modes for RTF document conversion,
 * each with different error handling and processing strategies.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public enum RtfConversionMode {
    
    /**
     * STRICT mode - Conversion fails immediately on any error
     * 
     * Use this mode when:
     * - Complete accuracy is required
     * - Any conversion error should stop the process
     * - You need to ensure all content is properly converted
     */
    STRICT("Strict mode - fails on any error"),
    
    /**
     * LOOSE mode - Conversion continues despite errors, attempting to extract maximum content
     * 
     * Use this mode when:
     * - Maximum content extraction is preferred over perfect accuracy
     * - Some conversion errors are acceptable
     * - You want to get as much content as possible from damaged or complex RTF files
     */
    LOOSE("Loose mode - continues on errors, extracts maximum content"),
    
    /**
     * BALANCED mode - Attempts strict conversion first, falls back to loose mode on errors
     * 
     * Use this mode when:
     * - You want the best of both worlds
     * - Prefer accuracy but accept fallback to partial conversion
     * - Default recommended mode for most use cases
     */
    BALANCED("Balanced mode - strict first, loose fallback");

    private final String description;

    /**
     * Constructor
     */
    RtfConversionMode(String description) {
        this.description = description;
    }

    /**
     * Gets the description of this conversion mode
     */
    public String getDescription() {
        return description;
    }

    /**
     * Checks if this mode allows error recovery
     */
    public boolean allowsErrorRecovery() {
        return this != STRICT;
    }

    /**
     * Checks if this mode requires strict processing
     */
    public boolean requiresStrictProcessing() {
        return this == STRICT;
    }

    /**
     * Gets the default conversion mode
     */
    public static RtfConversionMode getDefault() {
        return LOOSE;
    }

    @Override
    public String toString() {
        return name() + ": " + description;
    }
}
