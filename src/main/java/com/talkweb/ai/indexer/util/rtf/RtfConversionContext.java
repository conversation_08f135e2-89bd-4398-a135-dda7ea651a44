package com.talkweb.ai.indexer.util.rtf;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Context class for RTF to Markdown conversion
 * 
 * This class maintains the state and context information during
 * RTF document conversion, including formatting state, error tracking,
 * and conversion statistics.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class RtfConversionContext {

    private final RtfConversionMode mode;
    private final RtfConversionConfig config;
    private final File sourceFile;
    private final List<String> errors = new ArrayList<>();
    private final List<String> warnings = new ArrayList<>();
    private final Map<String, Object> metadata = new HashMap<>();
    private final Map<String, Object> conversionStats = new HashMap<>();

    // Formatting state
    private boolean inBold = false;
    private boolean inItalic = false;
    private boolean inUnderline = false;
    private boolean inStrikethrough = false;
    private boolean inSuperscript = false;
    private boolean inSubscript = false;
    private int currentFontSize = 12;
    private String currentFontFamily = null;
    private String currentColor = null;

    // Document structure state
    private int currentHeadingLevel = 0;
    private boolean inParagraph = false;
    private boolean inTable = false;
    private boolean inList = false;
    private int listLevel = 0;
    private boolean inHyperlink = false;

    // Conversion statistics
    private long startTime;
    private long endTime;
    private int totalCharacters = 0;
    private int totalParagraphs = 0;
    private int totalTables = 0;
    private int totalImages = 0;
    private int totalHyperlinks = 0;

    /**
     * Constructor
     */
    public RtfConversionContext(RtfConversionMode mode, RtfConversionConfig config, File sourceFile) {
        this.mode = mode != null ? mode : RtfConversionMode.getDefault();
        this.config = config != null ? config : new RtfConversionConfig();
        this.sourceFile = sourceFile;
        this.startTime = System.currentTimeMillis();
        
        initializeStats();
    }

    /**
     * Initializes conversion statistics
     */
    private void initializeStats() {
        conversionStats.put("startTime", startTime);
        conversionStats.put("totalCharacters", 0);
        conversionStats.put("totalParagraphs", 0);
        conversionStats.put("totalTables", 0);
        conversionStats.put("totalImages", 0);
        conversionStats.put("totalHyperlinks", 0);
        conversionStats.put("errorCount", 0);
        conversionStats.put("warningCount", 0);
    }

    // Getters and setters for mode, config, and file
    public RtfConversionMode getMode() {
        return mode;
    }

    public RtfConversionConfig getConfig() {
        return config;
    }

    public File getSourceFile() {
        return sourceFile;
    }

    public boolean isStrictMode() {
        return mode == RtfConversionMode.STRICT;
    }

    public boolean isLooseMode() {
        return mode == RtfConversionMode.LOOSE;
    }

    // Error and warning management
    public void addError(String error) {
        errors.add(error);
        conversionStats.put("errorCount", errors.size());
    }

    public void addWarning(String warning) {
        warnings.add(warning);
        conversionStats.put("warningCount", warnings.size());
    }

    public List<String> getErrors() {
        return new ArrayList<>(errors);
    }

    public List<String> getWarnings() {
        return new ArrayList<>(warnings);
    }

    public boolean hasErrors() {
        return !errors.isEmpty();
    }

    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }

    // Formatting state management
    public boolean isInBold() {
        return inBold;
    }

    public void setInBold(boolean inBold) {
        this.inBold = inBold;
    }

    public boolean isInItalic() {
        return inItalic;
    }

    public void setInItalic(boolean inItalic) {
        this.inItalic = inItalic;
    }

    public boolean isInUnderline() {
        return inUnderline;
    }

    public void setInUnderline(boolean inUnderline) {
        this.inUnderline = inUnderline;
    }

    public boolean isInStrikethrough() {
        return inStrikethrough;
    }

    public void setInStrikethrough(boolean inStrikethrough) {
        this.inStrikethrough = inStrikethrough;
    }

    public boolean isInSuperscript() {
        return inSuperscript;
    }

    public void setInSuperscript(boolean inSuperscript) {
        this.inSuperscript = inSuperscript;
    }

    public boolean isInSubscript() {
        return inSubscript;
    }

    public void setInSubscript(boolean inSubscript) {
        this.inSubscript = inSubscript;
    }

    public int getCurrentFontSize() {
        return currentFontSize;
    }

    public void setCurrentFontSize(int currentFontSize) {
        this.currentFontSize = currentFontSize;
    }

    public String getCurrentFontFamily() {
        return currentFontFamily;
    }

    public void setCurrentFontFamily(String currentFontFamily) {
        this.currentFontFamily = currentFontFamily;
    }

    public String getCurrentColor() {
        return currentColor;
    }

    public void setCurrentColor(String currentColor) {
        this.currentColor = currentColor;
    }

    // Document structure state management
    public int getCurrentHeadingLevel() {
        return currentHeadingLevel;
    }

    public void setCurrentHeadingLevel(int currentHeadingLevel) {
        this.currentHeadingLevel = Math.max(0, Math.min(6, currentHeadingLevel));
    }

    public boolean isInParagraph() {
        return inParagraph;
    }

    public void setInParagraph(boolean inParagraph) {
        this.inParagraph = inParagraph;
    }

    public boolean isInTable() {
        return inTable;
    }

    public void setInTable(boolean inTable) {
        this.inTable = inTable;
    }

    public boolean isInList() {
        return inList;
    }

    public void setInList(boolean inList) {
        this.inList = inList;
    }

    public int getListLevel() {
        return listLevel;
    }

    public void setListLevel(int listLevel) {
        this.listLevel = Math.max(0, listLevel);
    }

    public boolean isInHyperlink() {
        return inHyperlink;
    }

    public void setInHyperlink(boolean inHyperlink) {
        this.inHyperlink = inHyperlink;
    }

    // Statistics management
    public void incrementCharacterCount(int count) {
        totalCharacters += count;
        conversionStats.put("totalCharacters", totalCharacters);
    }

    public void incrementParagraphCount() {
        totalParagraphs++;
        conversionStats.put("totalParagraphs", totalParagraphs);
    }

    public void incrementTableCount() {
        totalTables++;
        conversionStats.put("totalTables", totalTables);
    }

    public void incrementImageCount() {
        totalImages++;
        conversionStats.put("totalImages", totalImages);
    }

    public void incrementHyperlinkCount() {
        totalHyperlinks++;
        conversionStats.put("totalHyperlinks", totalHyperlinks);
    }

    // Metadata management
    public void setMetadata(String key, Object value) {
        metadata.put(key, value);
    }

    public Object getMetadata(String key) {
        return metadata.get(key);
    }

    public Map<String, Object> getAllMetadata() {
        return new HashMap<>(metadata);
    }

    // Conversion completion
    public void markCompleted() {
        endTime = System.currentTimeMillis();
        conversionStats.put("endTime", endTime);
        conversionStats.put("durationMs", endTime - startTime);
    }

    public Map<String, Object> getConversionStats() {
        return new HashMap<>(conversionStats);
    }

    public long getDurationMs() {
        long end = endTime > 0 ? endTime : System.currentTimeMillis();
        return end - startTime;
    }

    @Override
    public String toString() {
        return "RtfConversionContext{" +
                "mode=" + mode +
                ", sourceFile=" + (sourceFile != null ? sourceFile.getName() : "null") +
                ", errors=" + errors.size() +
                ", warnings=" + warnings.size() +
                ", totalCharacters=" + totalCharacters +
                ", totalParagraphs=" + totalParagraphs +
                ", durationMs=" + getDurationMs() +
                '}';
    }
}
