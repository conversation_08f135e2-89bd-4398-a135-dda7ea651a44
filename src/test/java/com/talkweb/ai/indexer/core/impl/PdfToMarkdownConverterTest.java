package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.converter.ConversionContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class PdfToMarkdownConverterTest {

    private PdfToMarkdownConverter converter;
    @TempDir Path tempDir;

    @BeforeEach
    void setUp() {
        converter = new PdfToMarkdownConverter();
    }

    @Test
    void shouldSupportPdfFiles() {
        assertTrue(converter.getSupportedExtensions().contains("pdf"));
        assertFalse(converter.getSupportedExtensions().contains("docx"));
    }

    @Test
    void shouldConvertBasicText() throws Exception {
        File pdfFile = PdfTestUtils.createSimplePdf(tempDir, "test.pdf");
        ConversionContext context = ConversionContext.builder().build();
        var result = converter.convert(pdfFile, context);

        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());
        assertTrue(result.getMessage().endsWith(".md"),
            "Expected output path to end with .md, but got: " + result.getMessage());
        assertTrue(result.getContent().contains("Test PDF Document"));
    }

    @Test
    void shouldHandleInvalidPdf() {
        File invalidFile = tempDir.resolve("invalid.pdf").toFile();
        ConversionContext context = ConversionContext.builder().build();
        assertThrows(ConversionException.class, () -> converter.convert(invalidFile, context));
    }

    @Test
    void shouldHandleEmptyPdf() {
        ConversionContext context = ConversionContext.builder().build();
        assertThrows(ConversionException.class,
            () -> converter.convert(tempDir.resolve("empty.pdf").toFile(), context));
    }

    @Test
    void shouldHandleNullFile() {
        ConversionContext context = ConversionContext.builder().build();
        assertThrows(ConversionException.class, () -> converter.convert(null, context));
    }
}