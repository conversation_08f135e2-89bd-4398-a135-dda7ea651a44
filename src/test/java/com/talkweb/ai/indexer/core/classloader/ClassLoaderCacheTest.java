package com.talkweb.ai.indexer.core.classloader;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ClassLoaderCache测试
 */
public class ClassLoaderCacheTest {

    private ClassLoaderCache cache;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        cache = new ClassLoaderCache(10, 1000, 500); // 小的配置用于测试
    }

    @AfterEach
    void tearDown() {
        if (cache != null) {
            cache.shutdown();
        }
    }

    @Test
    void testPutAndGet() throws IOException {
        // Arrange
        String key = "test-plugin";
        URLClassLoader classLoader = createTestClassLoader();

        // Act
        cache.put(key, classLoader);
        URLClassLoader retrieved = cache.get(key);

        // Assert
        assertSame(classLoader, retrieved);
        assertTrue(cache.containsKey(key));
        assertEquals(1, cache.size());
    }

    @Test
    void testCacheMiss() {
        // Act
        URLClassLoader retrieved = cache.get("non-existent-key");

        // Assert
        assertNull(retrieved);
        assertFalse(cache.containsKey("non-existent-key"));
    }

    @Test
    void testRemove() throws IOException {
        // Arrange
        String key = "test-plugin";
        URLClassLoader classLoader = createTestClassLoader();
        cache.put(key, classLoader);

        // Act
        URLClassLoader removed = cache.remove(key);

        // Assert
        assertSame(classLoader, removed);
        assertFalse(cache.containsKey(key));
        assertEquals(0, cache.size());
    }

    @Test
    void testClear() throws IOException {
        // Arrange
        cache.put("plugin1", createTestClassLoader());
        cache.put("plugin2", createTestClassLoader());

        // Act
        cache.clear();

        // Assert
        assertEquals(0, cache.size());
        assertFalse(cache.containsKey("plugin1"));
        assertFalse(cache.containsKey("plugin2"));
    }

    @Test
    void testStatistics() throws IOException {
        // Arrange
        String key1 = "plugin1";
        String key2 = "plugin2";
        URLClassLoader classLoader1 = createTestClassLoader();
        URLClassLoader classLoader2 = createTestClassLoader();

        // Act
        cache.put(key1, classLoader1);
        cache.put(key2, classLoader2);
        
        // 缓存命中
        cache.get(key1);
        cache.get(key1);
        
        // 缓存未命中
        cache.get("non-existent");

        ClassLoaderCache.CacheStatistics stats = cache.getStatistics();

        // Assert
        assertEquals(2, stats.getSize());
        assertEquals(2, stats.getHits());
        assertEquals(1, stats.getMisses());
        assertEquals(2.0 / 3.0, stats.getHitRate(), 0.01);
    }

    @Test
    void testMaxCacheSize() throws IOException {
        // Arrange
        ClassLoaderCache smallCache = new ClassLoaderCache(2, 10000, 1000);

        try {
            // Act - 添加超过最大缓存大小的项
            smallCache.put("plugin1", createTestClassLoader());
            smallCache.put("plugin2", createTestClassLoader());
            smallCache.put("plugin3", createTestClassLoader()); // 应该触发LRU驱逐

            // Assert
            assertEquals(2, smallCache.size()); // 缓存大小应该保持在最大值
            
            ClassLoaderCache.CacheStatistics stats = smallCache.getStatistics();
            assertTrue(stats.getEvictions() > 0); // 应该有驱逐发生

        } finally {
            smallCache.shutdown();
        }
    }

    @Test
    void testWeakReference() throws IOException, InterruptedException {
        // Arrange
        String key = "test-plugin";
        URLClassLoader classLoader = createTestClassLoader();
        cache.put(key, classLoader);

        // Act - 移除强引用并触发垃圾回收
        classLoader = null;
        System.gc();
        Thread.sleep(100); // 给垃圾回收一些时间

        // 尝试多次垃圾回收
        for (int i = 0; i < 5; i++) {
            System.gc();
            Thread.sleep(50);
        }

        // Assert - 由于使用了WeakReference，类加载器可能被回收
        // 这个测试可能不稳定，因为垃圾回收的时机不确定
        URLClassLoader retrieved = cache.get(key);
        // 如果类加载器被回收，retrieved应该为null
        // 但这不是保证的，所以我们只检查缓存的基本功能
        assertTrue(retrieved == null || cache.containsKey(key));
    }

    @Test
    void testIdleTimeCleanup() throws IOException, InterruptedException {
        // Arrange
        ClassLoaderCache shortIdleCache = new ClassLoaderCache(10, 100, 50); // 100ms空闲时间，50ms清理间隔

        try {
            String key = "test-plugin";
            URLClassLoader classLoader = createTestClassLoader();
            shortIdleCache.put(key, classLoader);

            // Act - 等待超过空闲时间
            Thread.sleep(200);

            // Assert - 项应该被清理
            // 注意：这个测试可能不稳定，因为清理是异步的
            URLClassLoader retrieved = shortIdleCache.get(key);
            // 由于清理是异步的，我们不能保证项一定被清理
            // 但我们可以验证缓存仍然正常工作
            assertNotNull(shortIdleCache.getStatistics());

        } finally {
            shortIdleCache.shutdown();
        }
    }

    @Test
    void testConcurrentAccess() throws IOException, InterruptedException {
        // Arrange
        int threadCount = 10;
        int operationsPerThread = 100;
        Thread[] threads = new Thread[threadCount];

        // Act
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String key = "plugin-" + threadIndex + "-" + j;
                        URLClassLoader classLoader = createTestClassLoader();
                        
                        cache.put(key, classLoader);
                        URLClassLoader retrieved = cache.get(key);
                        assertSame(classLoader, retrieved);
                        
                        if (j % 10 == 0) {
                            cache.remove(key);
                        }
                    }
                } catch (IOException e) {
                    fail("IOException in thread: " + e.getMessage());
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join(5000); // 5秒超时
        }

        // Assert
        ClassLoaderCache.CacheStatistics stats = cache.getStatistics();
        assertTrue(stats.getHits() > 0);
        assertTrue(stats.getSize() >= 0);
    }

    @Test
    void testShutdown() throws IOException {
        // Arrange
        cache.put("plugin1", createTestClassLoader());
        cache.put("plugin2", createTestClassLoader());

        // Act
        cache.shutdown();

        // Assert
        assertEquals(0, cache.size());
        
        // 验证shutdown后的操作
        cache.put("plugin3", createTestClassLoader());
        // 由于executor已关闭，put操作可能不会正常工作，但不应该抛出异常
    }

    /**
     * 创建测试用的类加载器
     */
    private URLClassLoader createTestClassLoader() throws IOException {
        Path testJar = tempDir.resolve("test-" + System.nanoTime() + ".jar");
        Files.createFile(testJar);
        URL testUrl = testJar.toUri().toURL();
        return new URLClassLoader(new URL[]{testUrl}, getClass().getClassLoader());
    }
}
