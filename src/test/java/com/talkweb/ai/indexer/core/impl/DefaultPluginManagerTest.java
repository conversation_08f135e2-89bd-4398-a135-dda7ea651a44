package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginConfig;
import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collection;
import java.util.Properties;
import java.util.jar.JarOutputStream;
import java.util.zip.ZipEntry;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class DefaultPluginManagerTest {

    private DefaultPluginManager pluginManager;
    private PluginConfig mockConfig;

    @TempDir
    Path tempDir;

    private Path pluginsDir;

    @BeforeEach
    void setUp() throws IOException {
        mockConfig = mock(PluginConfig.class);
        pluginsDir = tempDir.resolve("plugins");
        Files.createDirectories(pluginsDir);
        when(mockConfig.getPluginsDir()).thenReturn(pluginsDir);
        pluginManager = new DefaultPluginManager(mockConfig);
    }

    @Test
    void testInstallAndUninstallPlugin() throws PluginException, IOException {
        Path pluginJar = createTestPluginJar("test-plugin", "com.talkweb.ai.indexer.core.impl.TestPlugin");

        pluginManager.installPlugin(pluginJar, false);
        assertEquals(1, pluginManager.getPlugins().size());
        // The TestPlugin class uses "lifecycle-plugin" as its hardcoded ID
        assertTrue(pluginManager.getPlugin("lifecycle-plugin").isPresent());

        assertTrue(pluginManager.uninstallPlugin("lifecycle-plugin", false));
        assertEquals(0, pluginManager.getPlugins().size());
        assertFalse(pluginManager.getPlugin("lifecycle-plugin").isPresent());
    }

    @Test
    void testLoadPlugins() throws IOException, PluginException {
        // Clear any existing plugins first
        try {
            pluginManager.destroyPlugins();
        } catch (Exception e) {
            // Ignore if no plugins to destroy
        }

        // Create only one test plugin jar since TestPlugin has a hardcoded ID
        // Creating multiple plugins with the same class would cause duplicate ID conflicts
        createTestPluginJar("lifecycle-plugin", "com.talkweb.ai.indexer.core.impl.TestPlugin");

        pluginManager.loadPlugins();

        // Verify that exactly one plugin was loaded
        assertEquals(1, pluginManager.getPlugins().size(), "Exactly one plugin should be loaded");

        // The TestPlugin class uses "lifecycle-plugin" as its hardcoded ID
        Collection<Plugin> loadedPlugins = pluginManager.getPlugins();
        Plugin plugin = loadedPlugins.iterator().next();
        assertEquals("lifecycle-plugin", plugin.getMetadata().getId());
        assertEquals(PluginState.LOADED, plugin.getState());
    }

    @Test
    void testPluginLifecycle() throws PluginException, IOException {
        // Clear any existing plugins first
        try {
            pluginManager.destroyPlugins();
        } catch (Exception e) {
            // Ignore if no plugins to destroy
        }

        // Create a test plugin jar with a specific ID
        Path pluginJar = createTestPluginJar("lifecycle-plugin", "com.talkweb.ai.indexer.core.impl.TestPlugin");

        // Install the plugin
        pluginManager.installPlugin(pluginJar, false);

        // The plugin loading mechanism creates mock plugins for com.example.TestPlugin
        // but the plugin ID comes from the plugin.properties file
        // Let's get the first plugin that was loaded and test its lifecycle
        Collection<Plugin> loadedPlugins = pluginManager.getPlugins();
        assertTrue(loadedPlugins.size() > 0, "At least one plugin should be installed");

        Plugin plugin = loadedPlugins.iterator().next();
        assertEquals(PluginState.LOADED, plugin.getState());

        // Test the plugin lifecycle
        pluginManager.initPlugins();
        assertEquals(PluginState.READY, plugin.getState());

        pluginManager.startPlugins();
        assertEquals(PluginState.RUNNING, plugin.getState());

        pluginManager.stopPlugins();
        assertEquals(PluginState.STOPPED, plugin.getState());

        pluginManager.destroyPlugins();
        // After destroying plugins, the plugins list should be empty
        assertTrue(pluginManager.getPlugins().isEmpty(), "Plugin list should be empty after destroying plugins");
    }

    private Path createTestPluginJar(String pluginId, String className) throws IOException {
        Path jarPath = pluginsDir.resolve(pluginId + ".jar");
        try (OutputStream fos = Files.newOutputStream(jarPath);
             JarOutputStream jos = new JarOutputStream(fos)) {

            // Write plugin.properties
            jos.putNextEntry(new ZipEntry("plugin.properties"));
            Properties props = new Properties();
            props.setProperty("plugin.id", pluginId);
            props.setProperty("plugin.name", pluginId);
            props.setProperty("plugin.version", "1.0.0");
            props.setProperty("plugin.class", "com.talkweb.ai.indexer.core.impl.TestPlugin");
            props.store(jos, null);
            jos.closeEntry();

            // Write TestPlugin class
            jos.putNextEntry(new ZipEntry("com/talkweb/ai/indexer/core/impl/TestPlugin.class"));
            jos.write(getTestPluginBytecode());
            jos.closeEntry();
        }
        return jarPath;
    }

    private byte[] getTestPluginBytecode() throws IOException {
        // In a real test, you would compile a TestPlugin class. For simplicity, we load it from the classpath.
        String resourceName = "/" + TestPlugin.class.getName().replace('.', '/') + ".class";
        try (InputStream is = getClass().getResourceAsStream(resourceName)) {
            if (is == null) {
                throw new IOException("Cannot find resource: " + resourceName);
            }
            return is.readAllBytes();
        }
    }
}
