package com.talkweb.ai.indexer.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.function.BooleanSupplier;

/**
 * Utility class for improving test stability and reliability.
 */
public class TestUtils {

    /**
     * Ensures file operations are stable by adding appropriate delays.
     * This helps with file system timing issues across different platforms.
     */
    public static void ensureFileStability() throws InterruptedException {
        Thread.sleep(100);
    }

    /**
     * Ensures file operations are stable with custom delay.
     */
    public static void ensureFileStability(long millis) throws InterruptedException {
        Thread.sleep(millis);
    }

    /**
     * Waits for a condition to become true with timeout and polling.
     * This is useful for waiting for asynchronous operations to complete.
     */
    public static boolean waitForCondition(BooleanSupplier condition, Duration timeout, Duration pollInterval) 
            throws InterruptedException {
        long timeoutMillis = timeout.toMillis();
        long pollMillis = pollInterval.toMillis();
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < timeoutMillis) {
            if (condition.getAsBoolean()) {
                return true;
            }
            Thread.sleep(pollMillis);
        }
        return false;
    }

    /**
     * Waits for a condition with default polling interval of 100ms.
     */
    public static boolean waitForCondition(BooleanSupplier condition, Duration timeout) 
            throws InterruptedException {
        return waitForCondition(condition, timeout, Duration.ofMillis(100));
    }

    /**
     * Creates a file with content and ensures it's stable.
     */
    public static void createStableFile(Path filePath, String content) throws IOException, InterruptedException {
        Files.writeString(filePath, content);
        ensureFileStability();
    }

    /**
     * Modifies a file with new content and ensures the change is detectable.
     */
    public static void modifyFileStably(Path filePath, String newContent) throws IOException, InterruptedException {
        // Ensure some time has passed since last modification
        ensureFileStability();
        Files.writeString(filePath, newContent);
        ensureFileStability();
    }

    /**
     * Verifies that a file exists and has the expected content.
     */
    public static boolean verifyFileContent(Path filePath, String expectedContent) {
        try {
            if (!Files.exists(filePath)) {
                return false;
            }
            String actualContent = Files.readString(filePath);
            return expectedContent.equals(actualContent);
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * Waits for a file to exist with timeout.
     */
    public static boolean waitForFileExists(Path filePath, Duration timeout) throws InterruptedException {
        return waitForCondition(() -> Files.exists(filePath), timeout);
    }

    /**
     * Waits for a file to be deleted with timeout.
     */
    public static boolean waitForFileDeleted(Path filePath, Duration timeout) throws InterruptedException {
        return waitForCondition(() -> !Files.exists(filePath), timeout);
    }

    /**
     * Waits for a file to have specific content with timeout.
     */
    public static boolean waitForFileContent(Path filePath, String expectedContent, Duration timeout) 
            throws InterruptedException {
        return waitForCondition(() -> verifyFileContent(filePath, expectedContent), timeout);
    }

    /**
     * Retries a test operation up to maxAttempts times.
     * This is useful for flaky tests that might fail due to timing issues.
     */
    public static <T extends Exception> void retryTest(TestOperation<T> operation, int maxAttempts, 
            Class<T> expectedExceptionType) throws T, InterruptedException {
        T lastException = null;
        
        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                operation.execute();
                return; // Success
            } catch (Exception e) {
                if (expectedExceptionType.isInstance(e)) {
                    lastException = expectedExceptionType.cast(e);
                    if (attempt < maxAttempts) {
                        // Wait before retry
                        Thread.sleep(100 * attempt); // Exponential backoff
                        continue;
                    }
                } else {
                    // Unexpected exception, rethrow immediately
                    throw new RuntimeException("Unexpected exception during test retry", e);
                }
            }
        }
        
        // All attempts failed
        if (lastException != null) {
            throw lastException;
        }
    }

    /**
     * Functional interface for test operations that can throw exceptions.
     */
    @FunctionalInterface
    public interface TestOperation<T extends Exception> {
        void execute() throws T;
    }

    /**
     * Ensures proper cleanup of resources with error handling.
     */
    public static void safeCleanup(AutoCloseable... resources) {
        for (AutoCloseable resource : resources) {
            if (resource != null) {
                try {
                    resource.close();
                } catch (Exception e) {
                    // Log but don't fail the test
                    System.err.println("Warning: Failed to close resource: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Creates a temporary file with unique content to avoid caching issues.
     */
    public static Path createUniqueTestFile(Path parentDir, String baseName, String content) 
            throws IOException, InterruptedException {
        String uniqueContent = content + "\n# Created at: " + System.currentTimeMillis();
        Path filePath = parentDir.resolve(baseName + "_" + System.nanoTime() + ".txt");
        createStableFile(filePath, uniqueContent);
        return filePath;
    }
}
