package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.core.DocumentProcessor;
import com.talkweb.ai.indexer.core.ProcessingContext;
import com.talkweb.ai.indexer.core.ProcessingResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Tests for AI-enhanced document processor
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class AiEnhancedDocumentProcessorTest {

    @Mock
    private DocumentSummaryService summaryService;

    @Mock
    private DocumentEmbeddingService embeddingService;

    @Mock
    private DocumentProcessor baseProcessor;

    @Mock
    private ProcessingContext context;

    private AiEnhancedDocumentProcessor processor;
    private File testFile;

    @BeforeEach
    void setUp() {
        processor = new AiEnhancedDocumentProcessor();
        testFile = new File("test.txt");
    }

    @Test
    void testProcessWithoutAiServices() throws Exception {
        // Given
        ProcessingResult baseResult = ProcessingResult.success(new File("output.md"));
        when(baseProcessor.process(testFile, context)).thenReturn(baseResult);

        // When
        ProcessingResult result = processor.processWithAiEnhancement(baseProcessor, testFile, context);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(baseResult, result);
    }

    @Test
    void testProcessWithFailedBaseProcessing() throws Exception {
        // Given
        ProcessingResult baseResult = ProcessingResult.failure("Processing failed");
        when(baseProcessor.process(testFile, context)).thenReturn(baseResult);

        // When
        ProcessingResult result = processor.processWithAiEnhancement(baseProcessor, testFile, context);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("Processing failed", result.getErrorMessage());
    }

    @Test
    void testGetAiServiceStatus() {
        // Given
        processor = new AiEnhancedDocumentProcessor();

        // When - without services
        Map<String, Boolean> status = processor.getAiServiceStatus();

        // Then
        assertNotNull(status);
        assertFalse(status.get("summary_service"));
        assertFalse(status.get("embedding_service"));
        assertFalse(status.get("ai_enabled"));
    }

    @Test
    void testGetAiServiceStatusWithServices() throws Exception {
        // Given
        processor = new AiEnhancedDocumentProcessor();
        setField(processor, "summaryService", summaryService);
        setField(processor, "embeddingService", embeddingService);

        // When
        Map<String, Boolean> status = processor.getAiServiceStatus();

        // Then
        assertNotNull(status);
        assertTrue(status.get("summary_service"));
        assertTrue(status.get("embedding_service"));
        assertTrue(status.get("ai_enabled"));
    }

    @Test
    void testIsAiEnabled() throws Exception {
        // Given
        processor = new AiEnhancedDocumentProcessor();

        // When - without services
        assertFalse(processor.isAiEnabled());

        // When - with services
        setField(processor, "summaryService", summaryService);
        assertTrue(processor.isAiEnabled());
    }

    /**
     * Helper method to set private fields using reflection
     */
    private void setField(Object target, String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
}
