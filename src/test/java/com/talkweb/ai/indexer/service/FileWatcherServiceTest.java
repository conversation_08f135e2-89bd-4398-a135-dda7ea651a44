package com.talkweb.ai.indexer.service;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

class FileWatcherServiceTest {

    @TempDir
    Path tempDir;

    private FileWatcherService fileWatcherService;

    @BeforeEach
    void setUp() throws IOException {
        fileWatcherService = new FileWatcherService(tempDir);
    }

    @AfterEach
    void tearDown() {
        if (fileWatcherService != null) {
            fileWatcherService.stop();
        }
    }

    @Test
    void testFileCreatedEvent() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> createdFilePath = new AtomicReference<>();

        fileWatcherService.addFileCreatedListener(path -> {
            createdFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean eventReceived = latch.await(5, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File created event should be received");
        assertEquals(testFile, createdFilePath.get(), "Created file path should match");
    }

    @Test
    void testFileModifiedEvent() throws IOException, InterruptedException {
        // Arrange
        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Initial content");

        // Ensure file is fully written and timestamp is stable
        Thread.sleep(100);

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> modifiedFilePath = new AtomicReference<>();

        fileWatcherService.addFileModifiedListener(path -> {
            modifiedFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        // Add a small delay to ensure the watcher has established baseline state
        Thread.sleep(100);

        Files.writeString(testFile, "Modified content");

        // Assert
        boolean eventReceived = latch.await(10, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File modified event should be received within the timeout");
        assertEquals(testFile, modifiedFilePath.get(), "The path of the modified file should be correct");
    }

    @Test
    void testFileDeletedEvent() throws IOException, InterruptedException {
        // Arrange
        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> deletedFilePath = new AtomicReference<>();

        fileWatcherService.addFileDeletedListener(path -> {
            deletedFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Files.delete(testFile);

        // Assert
        boolean eventReceived = latch.await(10, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File deleted event should be received within the timeout");
        assertEquals(testFile, deletedFilePath.get(), "The path of the deleted file should be correct");
    }

    @Test
    void testMultipleListeners() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch createdLatch = new CountDownLatch(2);

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean allEventsReceived = createdLatch.await(5, TimeUnit.SECONDS);
        assertTrue(allEventsReceived, "All file created events should be received by multiple listeners");
    }

    @Test
    void testStartStopService() throws IOException {
        // Act & Assert - No exception should be thrown
        fileWatcherService.start();
        fileWatcherService.start(); // Starting again should be no-op
        fileWatcherService.stop();
        fileWatcherService.stop(); // Stopping again should be no-op
    }

    @Test
    void testListenerExceptionHandling() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch latch = new CountDownLatch(1);

        // First listener throws exception
        fileWatcherService.addFileCreatedListener(path -> {
            throw new RuntimeException("Test exception");
        });

        // Second listener should still be called
        fileWatcherService.addFileCreatedListener(path -> {
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean eventReceived = latch.await(5, TimeUnit.SECONDS);
        assertTrue(eventReceived, "Second listener should be called even if first listener throws exception");
    }

    @Test
    void testWatcherModeDetection() throws IOException {
        // Test that we can detect which mode the watcher is using
        fileWatcherService.start();

        // The watcher should be running
        assertTrue(fileWatcherService.isRunning(), "Watcher should be running after start");

        // Should have some mode (native or polling)
        boolean hasMode = fileWatcherService.isUsingNativeWatcher() || !fileWatcherService.isUsingNativeWatcher();
        assertTrue(hasMode, "Watcher should have a defined mode");

        fileWatcherService.stop();
        assertFalse(fileWatcherService.isRunning(), "Watcher should not be running after stop");
    }

    @Test
    void testPollingIntervalAdjustment() throws IOException, InterruptedException {
        // Test that polling interval can be monitored
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        long initialInterval = fileWatcherService.getCurrentPollInterval();
        assertTrue(initialInterval > 0, "Poll interval should be positive");

        // Give some time for potential adjustments
        Thread.sleep(1000);

        long currentInterval = fileWatcherService.getCurrentPollInterval();
        assertTrue(currentInterval > 0, "Poll interval should remain positive");
    }

    @Test
    void testFileTrackingCount() throws IOException, InterruptedException {
        // Test file tracking functionality
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        int initialCount = fileWatcherService.getTrackedFileCount();

        // Create a test file
        Path testFile = tempDir.resolve("tracking-test.txt");
        Files.writeString(testFile, "Test content");

        // Give time for the file to be detected
        Thread.sleep(1000);

        int newCount = fileWatcherService.getTrackedFileCount();
        assertTrue(newCount >= initialCount, "File count should not decrease after adding file");
    }

    @Test
    void testConcurrentListenerExecution() throws IOException, InterruptedException {
        // Test that multiple listeners can execute concurrently without issues
        int listenerCount = 5;
        CountDownLatch latch = new CountDownLatch(listenerCount);

        for (int i = 0; i < listenerCount; i++) {
            final int listenerId = i;
            fileWatcherService.addFileCreatedListener(path -> {
                try {
                    // Simulate some processing time
                    Thread.sleep(10);
                    latch.countDown();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }

        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("concurrent-test.txt");
        Files.writeString(testFile, "Test content");

        boolean allListenersExecuted = latch.await(10, TimeUnit.SECONDS);
        assertTrue(allListenersExecuted, "All listeners should execute within timeout");
    }

    @Test
    void testFileModifiedEventWithEnhancedStability() throws IOException, InterruptedException {
        // Arrange - Create test file first
        Path testFile = tempDir.resolve("stable-test-" + System.currentTimeMillis() + ".txt");
        Files.writeString(testFile, "Initial content");

        // Ensure file is fully written and timestamp is stable
        Thread.sleep(100);

        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> modifiedFilePath = new AtomicReference<>();

        fileWatcherService.addFileModifiedListener(path -> {
            modifiedFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        // Ensure watcher has established baseline state
        Thread.sleep(200);

        // Modify file with enhanced stability
        Files.writeString(testFile, "Modified content with timestamp: " + System.currentTimeMillis());

        // Assert with enhanced waiting
        boolean eventReceived = latch.await(10, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File modified event should be received within timeout");
        assertEquals(testFile, modifiedFilePath.get(), "Modified file path should match");
    }

    @Test
    void testRobustFileOperations() throws IOException, InterruptedException {
        // Test handling of rapid file operations
        CountDownLatch createdLatch = new CountDownLatch(1);
        CountDownLatch modifiedLatch = new CountDownLatch(1);
        CountDownLatch deletedLatch = new CountDownLatch(1);

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileModifiedListener(path -> modifiedLatch.countDown());
        fileWatcherService.addFileDeletedListener(path -> deletedLatch.countDown());

        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("robust-test.txt");

        // Rapid operations
        Files.writeString(testFile, "Initial");
        assertTrue(createdLatch.await(5, TimeUnit.SECONDS), "File creation should be detected");

        Thread.sleep(100); // Small delay between operations
        Files.writeString(testFile, "Modified");
        assertTrue(modifiedLatch.await(5, TimeUnit.SECONDS), "File modification should be detected");

        Thread.sleep(100);
        Files.delete(testFile);
        assertTrue(deletedLatch.await(5, TimeUnit.SECONDS), "File deletion should be detected");
    }

    /**
     * Helper method to wait for a condition with timeout
     */
    private boolean waitForCondition(java.util.function.BooleanSupplier condition, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (condition.getAsBoolean()) {
                return true;
            }
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;
    }
}
