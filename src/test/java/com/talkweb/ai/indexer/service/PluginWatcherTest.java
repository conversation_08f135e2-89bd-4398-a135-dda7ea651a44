package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mockito;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class PluginWatcherTest {

    @TempDir
    Path tempDir;

    private PluginManager mockPluginManager;
    private PluginWatcher pluginWatcher;
    private CountDownLatch latch;

    @BeforeEach
    void setUp() throws IOException {
        mockPluginManager = Mockito.mock(PluginManager.class);
        // 使用较短的防抖时间，以便测试能够快速运行
        pluginWatcher = new PluginWatcher(mockPluginManager, tempDir, 10); // 使用极短的防抖时间
        latch = new CountDownLatch(1);

        // 设置模拟对象的行为
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(mockPluginManager).reloadPlugins();
    }

    @AfterEach
    void tearDown() {
        if (pluginWatcher != null) {
            pluginWatcher.stop();
        }
    }

    @Test
    public void testPluginFileCreated() throws IOException, InterruptedException {
        // Arrange
        pluginWatcher.start();

        // Act
        Path pluginFile = tempDir.resolve("test-plugin.jar");
        Files.writeString(pluginFile, "Mock JAR content");

        // Assert - 等待latch计数为0或超时
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Plugin reload was not triggered");
        verify(mockPluginManager, times(1)).reloadPlugins();
    }

    @Test
    public void testPluginFileModified() throws IOException, InterruptedException {
        // Arrange
        Path pluginFile = tempDir.resolve("test-plugin.jar");
        Files.writeString(pluginFile, "Initial content");
        // 确保文件已完全写入
        Thread.sleep(100);
        pluginWatcher.start();

        // Act
        Files.writeString(pluginFile, "Modified content");

        // Assert - 等待latch计数为0或超时
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Plugin reload was not triggered");
        verify(mockPluginManager, times(1)).reloadPlugins();
    }

    @Test
    public void testPluginFileDeleted() throws IOException, InterruptedException {
        // Arrange
        Path pluginFile = tempDir.resolve("test-plugin.jar");
        Files.writeString(pluginFile, "Test content");
        // 确保文件已完全写入
        Thread.sleep(100);
        pluginWatcher.start();

        // Act
        Files.delete(pluginFile);

        Thread.sleep(1000); // 给一些时间让所有可能的调用发生
        // Assert - 等待latch计数为0或超时
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Plugin reload was not triggered");
        // File deletion might trigger both modification and deletion events, so allow up to 2 calls
        verify(mockPluginManager, atMost(2)).reloadPlugins();
        verify(mockPluginManager, atLeast(1)).reloadPlugins();
    }

    @Test
    public void testNonPluginFileIgnored() throws IOException, InterruptedException {
        // Arrange
        AtomicBoolean reloadCalled = new AtomicBoolean(false);
        doAnswer(invocation -> {
            reloadCalled.set(true);
            return null;
        }).when(mockPluginManager).reloadPlugins();

        pluginWatcher.start();

        // Act
        Path textFile = tempDir.resolve("test-file.txt");
        Files.writeString(textFile, "This is not a plugin");

        // Assert - 给足够的时间让事件可能被处理，但预期是不会处理
        Thread.sleep(300);
        assertFalse(reloadCalled.get(), "Plugin reload should not be triggered for non-plugin files");
        verify(mockPluginManager, never()).reloadPlugins();
    }

    @Test
    public void testDebouncing() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch debounceLatch = new CountDownLatch(1);
        AtomicBoolean reloadCalled = new AtomicBoolean(false);
        doAnswer(invocation -> {
            if (!reloadCalled.getAndSet(true)) {
                // 只在第一次调用时倒计时
                debounceLatch.countDown();
            }
            return null;
        }).when(mockPluginManager).reloadPlugins();

        pluginWatcher.start();

        // Act - 创建多个插件文件，间隔很短
        for (int i = 0; i < 5; i++) {
            Path pluginFile = tempDir.resolve("test-plugin-" + i + ".jar");
            Files.writeString(pluginFile, "Plugin content " + i);
            Thread.sleep(10); // 文件创建之间的短暂延迟
        }

        // Assert - 等待至少一次回调触发
        assertTrue(debounceLatch.await(5, TimeUnit.SECONDS), "Plugin reload was not triggered");
        // 由于防抖，应该只调用一两次
        Thread.sleep(1000); // 给一些时间让所有可能的调用发生
        verify(mockPluginManager, atMost(5)).reloadPlugins();
    }

    @Test
    public void testStartStop() throws IOException {
        // Act & Assert - 不应抛出异常
        pluginWatcher.start();
        pluginWatcher.start(); // 再次启动应该是空操作
        pluginWatcher.stop();
        pluginWatcher.stop(); // 再次停止应该是空操作
    }

    @Test
    public void testExceptionHandling() throws IOException, InterruptedException, PluginException {
        // Arrange
        CountDownLatch errorLatch = new CountDownLatch(1);
        CountDownLatch recoveryLatch = new CountDownLatch(1);

        doThrow(new RuntimeException("Test exception"))
            .when(mockPluginManager).reloadPlugins();

        // 当stopPlugins被调用时，标记恢复流程已开始
        doAnswer(invocation -> {
            errorLatch.countDown(); // 表示异常被处理
            return null;
        }).when(mockPluginManager).stopPlugins();

        // 当startPlugins被调用时，表示恢复流程已完成
        doAnswer(invocation -> {
            recoveryLatch.countDown();
            return null;
        }).when(mockPluginManager).startPlugins();

        // Act
        pluginWatcher.start();
        Path pluginFile = tempDir.resolve("test-plugin.jar");
        Files.writeString(pluginFile, "Mock JAR content");

        // Assert - 等待异常被处理
        assertTrue(errorLatch.await(5, TimeUnit.SECONDS), "Exception handling was not triggered");
        verify(mockPluginManager, timeout(3000)).reloadPlugins();

        // 等待恢复流程完成
        assertTrue(recoveryLatch.await(5, TimeUnit.SECONDS), "Recovery process was not completed");
        verify(mockPluginManager).stopPlugins();
        verify(mockPluginManager).destroyPlugins();
        verify(mockPluginManager).loadPlugins();
        verify(mockPluginManager).initPlugins();
        verify(mockPluginManager).startPlugins();

        // Act again - 在异常之后应该仍然工作
        reset(mockPluginManager);

        // 重新设置异常行为和验证锁
        CountDownLatch secondErrorLatch = new CountDownLatch(1);
        doThrow(new RuntimeException("Second test exception"))
            .when(mockPluginManager).reloadPlugins();
        doAnswer(invocation -> {
            secondErrorLatch.countDown();
            return null;
        }).when(mockPluginManager).stopPlugins();

        Files.writeString(pluginFile, "Updated content");

        // Assert - 再次等待异常处理
        assertTrue(secondErrorLatch.await(5, TimeUnit.SECONDS), "Second exception handling was not triggered");
        verify(mockPluginManager, timeout(3000)).reloadPlugins();
        verify(mockPluginManager).stopPlugins();
    }
}
