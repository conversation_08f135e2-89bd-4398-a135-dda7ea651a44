package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.util.odt.OdtConversionConfig;
import com.talkweb.ai.indexer.util.odt.OdtConversionMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Enhanced test class for improved ODT to Markdown converter
 * Tests ODT compatibility, structure conversion, and information extraction
 *
 * <AUTHOR> Assistant
 * @version 2.0
 */
class OdtToMarkdownConverterEnhancedTest {

    private OdtToMarkdownConverter converter;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        PluginMetadata metadata = PluginMetadata.builder()
                .id("odt-converter-enhanced-test")
                .name("ODT Converter Enhanced Test")
                .version("2.0.0")
                .description("Enhanced test ODT converter with improved features")
                .className("com.talkweb.ai.indexer.util.OdtToMarkdownConverter")
                .build();
        
        converter = new OdtToMarkdownConverter(metadata);
    }

    @Test
    void testBasicOdtConversion() throws Exception {
        // Configure for basic parsing
        OdtConversionConfig config = OdtConversionConfig.builder()
                .mode(OdtConversionMode.LOOSE)
                .useAdvancedParsing(false)
                .preserveFormatting(true)
                .convertTables(true)
                .build();
        converter.setConfig(config);
        
        File odtFile = createBasicOdtFile();
        
        ConversionResult result = converter.convert(odtFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("Basic ODT conversion result:\n" + content);
        
        // Should contain basic content
        assertFalse(content.trim().isEmpty());
    }

    @Test
    void testEnhancedOdtConversion() throws Exception {
        // Configure for enhanced parsing
        OdtConversionConfig config = OdtConversionConfig.builder()
                .mode(OdtConversionMode.LOOSE)
                .useAdvancedParsing(true)
                .preserveFormatting(true)
                .convertTables(true)
                .convertLists(true)
                .convertHyperlinks(true)
                .includeMetadata(true)
                .build();
        converter.setConfig(config);
        
        File odtFile = createEnhancedOdtFile();
        
        ConversionResult result = converter.convert(odtFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("Enhanced ODT conversion result:\n" + content);
        
        // Should contain enhanced features
        assertFalse(content.trim().isEmpty());
        assertTrue(content.length() > 50); // Should have substantial content
    }

    @Test
    void testOdtWithMetadata() throws Exception {
        OdtConversionConfig config = OdtConversionConfig.builder()
                .useAdvancedParsing(true)
                .includeMetadata(true)
                .build();
        converter.setConfig(config);
        
        File odtFile = createOdtWithMetadata();
        
        ConversionResult result = converter.convert(odtFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        String content = result.getContent();
        System.out.println("ODT with metadata result:\n" + content);
        
        // Should contain metadata section
        assertTrue(content.contains("---") || content.contains("title") || content.contains("author"));
    }

    @Test
    void testOdtWithTables() throws Exception {
        OdtConversionConfig config = OdtConversionConfig.builder()
                .useAdvancedParsing(true)
                .convertTables(true)
                .build();
        converter.setConfig(config);
        
        File odtFile = createOdtWithTable();
        
        ConversionResult result = converter.convert(odtFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        String content = result.getContent();
        System.out.println("ODT with table result:\n" + content);
        
        // Should contain table structure
        assertTrue(content.contains("|") || content.contains("table"));
    }

    @Test
    void testOdtWithLists() throws Exception {
        OdtConversionConfig config = OdtConversionConfig.builder()
                .useAdvancedParsing(true)
                .convertLists(true)
                .build();
        converter.setConfig(config);
        
        File odtFile = createOdtWithLists();
        
        ConversionResult result = converter.convert(odtFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        String content = result.getContent();
        System.out.println("ODT with lists result:\n" + content);
        
        // Should contain list markers
        assertTrue(content.contains("-") || content.contains("1.") || content.contains("list"));
    }

    @Test
    void testAdvancedVsBasicParsing() throws Exception {
        File odtFile = createEnhancedOdtFile();
        
        // Test with advanced parsing
        OdtConversionConfig advancedConfig = OdtConversionConfig.builder()
                .useAdvancedParsing(true)
                .preserveFormatting(true)
                .convertTables(true)
                .convertLists(true)
                .build();
        converter.setConfig(advancedConfig);
        
        ConversionResult advancedResult = converter.convert(odtFile);
        
        // Test with basic parsing
        OdtConversionConfig basicConfig = OdtConversionConfig.builder()
                .useAdvancedParsing(false)
                .preserveFormatting(false)
                .convertTables(false)
                .convertLists(false)
                .build();
        converter.setConfig(basicConfig);
        
        ConversionResult basicResult = converter.convert(odtFile);
        
        // Both should succeed
        assertTrue(advancedResult.isSuccess());
        assertTrue(basicResult.isSuccess());
        
        System.out.println("Advanced parsing result:\n" + advancedResult.getContent());
        System.out.println("Basic parsing result:\n" + basicResult.getContent());
        
        // Advanced parsing should generally produce more content
        assertNotNull(advancedResult.getContent());
        assertNotNull(basicResult.getContent());
    }

    /**
     * Creates a basic ODT file for testing
     */
    private File createBasicOdtFile() throws IOException {
        File odtFile = tempDir.resolve("basic.odt").toFile();
        
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(odtFile))) {
            // Add mimetype
            addMimeType(zos);
            
            // Add basic content.xml
            addBasicContent(zos);
            
            // Add manifest
            addManifest(zos);
        }
        
        return odtFile;
    }

    /**
     * Creates an enhanced ODT file with more features
     */
    private File createEnhancedOdtFile() throws IOException {
        File odtFile = tempDir.resolve("enhanced.odt").toFile();
        
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(odtFile))) {
            // Add mimetype
            addMimeType(zos);
            
            // Add enhanced content.xml
            addEnhancedContent(zos);
            
            // Add manifest
            addManifest(zos);
        }
        
        return odtFile;
    }

    /**
     * Creates ODT file with metadata
     */
    private File createOdtWithMetadata() throws IOException {
        File odtFile = tempDir.resolve("metadata.odt").toFile();
        
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(odtFile))) {
            // Add mimetype
            addMimeType(zos);
            
            // Add content.xml
            addBasicContent(zos);
            
            // Add meta.xml with metadata
            addMetadata(zos);
            
            // Add manifest
            addManifest(zos);
        }
        
        return odtFile;
    }

    /**
     * Creates ODT file with table
     */
    private File createOdtWithTable() throws IOException {
        File odtFile = tempDir.resolve("table.odt").toFile();
        
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(odtFile))) {
            // Add mimetype
            addMimeType(zos);
            
            // Add content.xml with table
            addTableContent(zos);
            
            // Add manifest
            addManifest(zos);
        }
        
        return odtFile;
    }

    /**
     * Creates ODT file with lists
     */
    private File createOdtWithLists() throws IOException {
        File odtFile = tempDir.resolve("lists.odt").toFile();
        
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(odtFile))) {
            // Add mimetype
            addMimeType(zos);
            
            // Add content.xml with lists
            addListContent(zos);
            
            // Add manifest
            addManifest(zos);
        }
        
        return odtFile;
    }

    private void addMimeType(ZipOutputStream zos) throws IOException {
        ZipEntry mimeEntry = new ZipEntry("mimetype");
        zos.putNextEntry(mimeEntry);
        zos.write("application/vnd.oasis.opendocument.text".getBytes("UTF-8"));
        zos.closeEntry();
    }

    private void addBasicContent(ZipOutputStream zos) throws IOException {
        ZipEntry contentEntry = new ZipEntry("content.xml");
        zos.putNextEntry(contentEntry);

        String content = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<office:document-content " +
                "xmlns:office=\"urn:oasis:names:tc:opendocument:xmlns:office:1.0\" " +
                "xmlns:text=\"urn:oasis:names:tc:opendocument:xmlns:text:1.0\">\n" +
                "<office:body>\n" +
                "<office:text>\n" +
                "<text:h text:outline-level=\"1\">Test Document</text:h>\n" +
                "<text:p>This is a test paragraph with some content.</text:p>\n" +
                "<text:p>This is another paragraph.</text:p>\n" +
                "</office:text>\n" +
                "</office:body>\n" +
                "</office:document-content>";

        zos.write(content.getBytes("UTF-8"));
        zos.closeEntry();
    }

    private void addEnhancedContent(ZipOutputStream zos) throws IOException {
        ZipEntry contentEntry = new ZipEntry("content.xml");
        zos.putNextEntry(contentEntry);

        String content = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<office:document-content " +
                "xmlns:office=\"urn:oasis:names:tc:opendocument:xmlns:office:1.0\" " +
                "xmlns:text=\"urn:oasis:names:tc:opendocument:xmlns:text:1.0\" " +
                "xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n" +
                "<office:body>\n" +
                "<office:text>\n" +
                "<text:h text:outline-level=\"1\">Enhanced Test Document</text:h>\n" +
                "<text:p>This document contains <text:span text:style-name=\"bold\">bold text</text:span> " +
                "and <text:span text:style-name=\"italic\">italic text</text:span>.</text:p>\n" +
                "<text:h text:outline-level=\"2\">Section with Link</text:h>\n" +
                "<text:p>Visit <text:a xlink:href=\"https://example.com\">our website</text:a> for more info.</text:p>\n" +
                "<text:h text:outline-level=\"3\">Subsection</text:h>\n" +
                "<text:p>This is a subsection with more content.</text:p>\n" +
                "</office:text>\n" +
                "</office:body>\n" +
                "</office:document-content>";

        zos.write(content.getBytes("UTF-8"));
        zos.closeEntry();
    }

    private void addMetadata(ZipOutputStream zos) throws IOException {
        ZipEntry metaEntry = new ZipEntry("meta.xml");
        zos.putNextEntry(metaEntry);

        String metadata = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<office:document-meta " +
                "xmlns:office=\"urn:oasis:names:tc:opendocument:xmlns:office:1.0\" " +
                "xmlns:meta=\"urn:oasis:names:tc:opendocument:xmlns:meta:1.0\" " +
                "xmlns:dc=\"http://purl.org/dc/elements/1.1/\">\n" +
                "<office:meta>\n" +
                "<dc:title>Test Document Title</dc:title>\n" +
                "<dc:creator>Test Author</dc:creator>\n" +
                "<dc:subject>Test Subject</dc:subject>\n" +
                "<dc:description>Test document description</dc:description>\n" +
                "<meta:creation-date>2023-12-15T10:30:00</meta:creation-date>\n" +
                "</office:meta>\n" +
                "</office:document-meta>";

        zos.write(metadata.getBytes("UTF-8"));
        zos.closeEntry();
    }

    private void addTableContent(ZipOutputStream zos) throws IOException {
        ZipEntry contentEntry = new ZipEntry("content.xml");
        zos.putNextEntry(contentEntry);

        String content = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<office:document-content " +
                "xmlns:office=\"urn:oasis:names:tc:opendocument:xmlns:office:1.0\" " +
                "xmlns:text=\"urn:oasis:names:tc:opendocument:xmlns:text:1.0\" " +
                "xmlns:table=\"urn:oasis:names:tc:opendocument:xmlns:table:1.0\">\n" +
                "<office:body>\n" +
                "<office:text>\n" +
                "<text:h text:outline-level=\"1\">Document with Table</text:h>\n" +
                "<text:p>Here is a table:</text:p>\n" +
                "<table:table>\n" +
                "<table:table-row>\n" +
                "<table:table-cell><text:p>Header 1</text:p></table:table-cell>\n" +
                "<table:table-cell><text:p>Header 2</text:p></table:table-cell>\n" +
                "<table:table-cell><text:p>Header 3</text:p></table:table-cell>\n" +
                "</table:table-row>\n" +
                "<table:table-row>\n" +
                "<table:table-cell><text:p>Cell 1</text:p></table:table-cell>\n" +
                "<table:table-cell><text:p>Cell 2</text:p></table:table-cell>\n" +
                "<table:table-cell><text:p>Cell 3</text:p></table:table-cell>\n" +
                "</table:table-row>\n" +
                "</table:table>\n" +
                "</office:text>\n" +
                "</office:body>\n" +
                "</office:document-content>";

        zos.write(content.getBytes("UTF-8"));
        zos.closeEntry();
    }

    private void addListContent(ZipOutputStream zos) throws IOException {
        ZipEntry contentEntry = new ZipEntry("content.xml");
        zos.putNextEntry(contentEntry);

        String content = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<office:document-content " +
                "xmlns:office=\"urn:oasis:names:tc:opendocument:xmlns:office:1.0\" " +
                "xmlns:text=\"urn:oasis:names:tc:opendocument:xmlns:text:1.0\">\n" +
                "<office:body>\n" +
                "<office:text>\n" +
                "<text:h text:outline-level=\"1\">Document with Lists</text:h>\n" +
                "<text:p>Unordered list:</text:p>\n" +
                "<text:list>\n" +
                "<text:list-item><text:p>First item</text:p></text:list-item>\n" +
                "<text:list-item><text:p>Second item</text:p></text:list-item>\n" +
                "<text:list-item><text:p>Third item</text:p></text:list-item>\n" +
                "</text:list>\n" +
                "<text:p>Ordered list:</text:p>\n" +
                "<text:list text:style-name=\"numbered\">\n" +
                "<text:list-item><text:p>First numbered item</text:p></text:list-item>\n" +
                "<text:list-item><text:p>Second numbered item</text:p></text:list-item>\n" +
                "<text:list-item><text:p>Third numbered item</text:p></text:list-item>\n" +
                "</text:list>\n" +
                "</office:text>\n" +
                "</office:body>\n" +
                "</office:document-content>";

        zos.write(content.getBytes("UTF-8"));
        zos.closeEntry();
    }

    private void addManifest(ZipOutputStream zos) throws IOException {
        ZipEntry manifestEntry = new ZipEntry("META-INF/manifest.xml");
        zos.putNextEntry(manifestEntry);

        String manifest = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<manifest:manifest xmlns:manifest=\"urn:oasis:names:tc:opendocument:xmlns:manifest:1.0\">\n" +
                "<manifest:file-entry manifest:full-path=\"/\" manifest:media-type=\"application/vnd.oasis.opendocument.text\"/>\n" +
                "<manifest:file-entry manifest:full-path=\"content.xml\" manifest:media-type=\"text/xml\"/>\n" +
                "<manifest:file-entry manifest:full-path=\"meta.xml\" manifest:media-type=\"text/xml\"/>\n" +
                "</manifest:manifest>";

        zos.write(manifest.getBytes("UTF-8"));
        zos.closeEntry();
    }
}
