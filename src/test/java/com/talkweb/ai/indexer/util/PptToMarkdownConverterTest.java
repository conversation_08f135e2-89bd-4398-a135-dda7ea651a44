package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.util.ppt.PptConversionConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for PowerPoint to Markdown converter
 * 
 * Tests various aspects of the conversion process including different file formats,
 * configuration options, and error handling scenarios.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
class PptToMarkdownConverterTest {
    
    private PptToMarkdownConverter converter;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        converter = new PptToMarkdownConverter();
    }
    
    @Test
    void testConverterInitialization() {
        assertNotNull(converter);
        assertNotNull(converter.getMetadata());
        assertEquals("ppt-to-markdown-converter", converter.getMetadata().getId());
    }
    
    @Test
    void testSupportedExtensions() {
        assertTrue(converter.supportsExtension("ppt"));
        assertTrue(converter.supportsExtension("pptx"));
        assertTrue(converter.supportsExtension("pptm"));
        assertTrue(converter.supportsExtension(".ppt"));
        assertTrue(converter.supportsExtension(".pptx"));
        assertTrue(converter.supportsExtension(".pptm"));
        
        assertFalse(converter.supportsExtension("doc"));
        assertFalse(converter.supportsExtension("pdf"));
        assertFalse(converter.supportsExtension("txt"));
        assertFalse(converter.supportsExtension(null));
    }
    
    @Test
    void testNullFileHandling() {
        assertThrows(IllegalArgumentException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(null);
        });
    }

    @Test
    void testNonExistentFileHandling() {
        File nonExistentFile = new File("non_existent_file.pptx");
        assertThrows(IllegalArgumentException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(nonExistentFile);
        });
    }

    @Test
    void testInvalidFileExtension() throws IOException {
        // Create a temporary file with wrong extension
        Path invalidFile = tempDir.resolve("test.txt");
        Files.write(invalidFile, "This is not a PowerPoint file".getBytes());

        assertThrows(IllegalArgumentException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(invalidFile.toFile());
        });
    }
    
    @Test
    void testEmptyFileHandling() throws IOException {
        // Create an empty PPT file
        Path emptyFile = tempDir.resolve("empty.pptx");
        Files.createFile(emptyFile);

        // Empty files should be rejected as invalid
        assertThrows(IllegalArgumentException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(emptyFile.toFile());
        });
    }
    
    @Test
    void testConfigurationOptions() {
        PptConversionConfig config = new PptConversionConfig();
        
        // Test default values
        assertTrue(config.isIncludeMetadata());
        assertFalse(config.isIncludeSpeakerNotes());
        assertTrue(config.isExtractImages());
        assertTrue(config.isExtractTables());
        assertFalse(config.isStrictMode());
        
        // Test configuration changes
        config.setIncludeMetadata(false)
              .setIncludeSpeakerNotes(true)
              .setExtractImages(false)
              .setStrictMode(true);
        
        assertFalse(config.isIncludeMetadata());
        assertTrue(config.isIncludeSpeakerNotes());
        assertFalse(config.isExtractImages());
        assertTrue(config.isStrictMode());
    }
    
    @Test
    void testPresetConfigurations() {
        // Test high fidelity config
        PptConversionConfig highFidelity = PptConversionConfig.createHighFidelityConfig();
        assertTrue(highFidelity.isIncludeMetadata());
        assertTrue(highFidelity.isIncludeSpeakerNotes());
        assertTrue(highFidelity.isExtractImages());
        assertTrue(highFidelity.isExtractTables());
        assertTrue(highFidelity.isExtractCharts());
        
        // Test performance config
        PptConversionConfig performance = PptConversionConfig.createPerformanceConfig();
        assertFalse(performance.isIncludeMetadata());
        assertFalse(performance.isIncludeSpeakerNotes());
        assertFalse(performance.isExtractImages());
        assertTrue(performance.isExtractTables());
        assertFalse(performance.isExtractCharts());
        
        // Test strict config
        PptConversionConfig strict = PptConversionConfig.createStrictConfig();
        assertTrue(strict.isStrictMode());
        assertFalse(strict.isSkipCorruptedSlides());
        assertTrue(strict.isLogDetailedErrors());
        assertTrue(strict.isGenerateErrorReport());
    }
    
    @Test
    void testImageConfiguration() {
        PptConversionConfig config = new PptConversionConfig();
        
        // Test default image settings
        assertEquals("images", config.getImageOutputDirectory());
        assertEquals("png", config.getImageFormat());
        assertEquals(800, config.getMaxImageWidth());
        assertEquals(600, config.getMaxImageHeight());
        
        // Test image configuration changes
        config.setImageOutputDirectory("custom_images")
              .setImageFormat("jpg")
              .setMaxImageWidth(1024)
              .setMaxImageHeight(768);
        
        assertEquals("custom_images", config.getImageOutputDirectory());
        assertEquals("jpg", config.getImageFormat());
        assertEquals(1024, config.getMaxImageWidth());
        assertEquals(768, config.getMaxImageHeight());
        
        // Test validation
        config.setMaxImageWidth(50); // Below minimum
        assertEquals(100, config.getMaxImageWidth()); // Should be clamped to minimum
        
        config.setImageOutputDirectory(null);
        assertEquals("images", config.getImageOutputDirectory()); // Should fallback to default
    }
    
    @Test
    void testPluginLifecycle() throws Exception {
        assertEquals(converter.getState().toString(), "STOPPED");
        
        converter.start();
        assertEquals(converter.getState().toString(), "RUNNING");
        
        converter.stop();
        assertEquals(converter.getState().toString(), "STOPPED");
        
        converter.destroy();
        assertEquals(converter.getState().toString(), "DESTROYED");
    }
    
    @Test
    void testConversionResultStructure() throws IOException {
        // Create a minimal valid-looking PPTX file (just for structure testing)
        Path testFile = tempDir.resolve("test.pptx");
        Files.write(testFile, createMinimalPptxBytes());
        
        try {
            ConversionResult result = converter.convert(testFile.toFile());
            
            assertNotNull(result);
            assertNotNull(result.getStatus());
            assertNotNull(result.getContent());

            // Check that content is not empty (even if conversion fails)
            assertNotNull(result.getContent());
            
        } catch (Exception e) {
            // Expected for invalid file, but we're testing structure
            assertTrue(e.getMessage().contains("Failed to convert PowerPoint file"));
        }
    }
    
    @Test
    void testCopyConstructor() {
        PptConversionConfig original = new PptConversionConfig()
            .setIncludeMetadata(false)
            .setExtractImages(false)
            .setStrictMode(true)
            .setImageFormat("jpg");
        
        PptConversionConfig copy = new PptConversionConfig(original);
        
        assertEquals(original.isIncludeMetadata(), copy.isIncludeMetadata());
        assertEquals(original.isExtractImages(), copy.isExtractImages());
        assertEquals(original.isStrictMode(), copy.isStrictMode());
        assertEquals(original.getImageFormat(), copy.getImageFormat());
        
        // Test null copy constructor
        PptConversionConfig nullCopy = new PptConversionConfig(null);
        assertNotNull(nullCopy);
        // Should have default values
        assertTrue(nullCopy.isIncludeMetadata());
    }
    
    @Test
    void testToStringMethods() {
        PptConversionConfig config = new PptConversionConfig();
        String configString = config.toString();
        
        assertNotNull(configString);
        assertTrue(configString.contains("PptConversionConfig"));
        assertTrue(configString.contains("includeMetadata"));
        assertTrue(configString.contains("strictMode"));
    }
    
    /**
     * Creates minimal bytes that look like a PPTX file for testing
     * (This is just for testing file handling, not actual conversion)
     */
    private byte[] createMinimalPptxBytes() {
        // PPTX files start with ZIP signature
        return new byte[]{0x50, 0x4B, 0x03, 0x04, 0x00, 0x00, 0x00, 0x00};
    }
    
    @Test
    void testErrorHandlingModes() throws IOException {
        Path invalidPptx = tempDir.resolve("invalid.pptx");
        Files.write(invalidPptx, createMinimalPptxBytes());
        
        // Test loose mode (should not throw)
        PptConversionConfig looseConfig = new PptConversionConfig().setStrictMode(false);
        assertDoesNotThrow(() -> {
            String result = PptToMarkdownConverter.convertToMarkdown(invalidPptx.toFile(), looseConfig);
            assertNotNull(result);
        });

        // Test strict mode (should throw)
        PptConversionConfig strictConfig = new PptConversionConfig().setStrictMode(true);
        assertThrows(RuntimeException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(invalidPptx.toFile(), strictConfig);
        });
    }
    
    @Test
    void testCacheConfiguration() {
        PptConversionConfig config = new PptConversionConfig();
        
        assertTrue(config.isEnableCaching());
        assertEquals(100, config.getMaxCacheSize());
        
        config.setEnableCaching(false).setMaxCacheSize(50);
        
        assertFalse(config.isEnableCaching());
        assertEquals(50, config.getMaxCacheSize());
        
        // Test minimum cache size validation
        config.setMaxCacheSize(5); // Below minimum
        assertEquals(10, config.getMaxCacheSize()); // Should be clamped to minimum
    }
    
    @Test
    void testTimeoutConfiguration() {
        PptConversionConfig config = new PptConversionConfig();

        assertEquals(300, config.getProcessingTimeout());

        config.setProcessingTimeout(600);
        assertEquals(600, config.getProcessingTimeout());

        // Test minimum timeout validation
        config.setProcessingTimeout(10); // Below minimum
        assertEquals(30, config.getProcessingTimeout()); // Should be clamped to minimum
    }

    // ========== Additional Comprehensive Test Cases ==========

    @Test
    void testAdvancedConfigurationOptions() {
        PptConversionConfig config = new PptConversionConfig();

        // Test text processing options
        assertTrue(config.isNormalizeWhitespace());
        assertTrue(config.isConvertBulletsToMarkdown());
        assertTrue(config.isPreserveTextBoxes());
        assertTrue(config.isExtractTextFromShapes());

        config.setNormalizeWhitespace(false)
              .setConvertBulletsToMarkdown(false)
              .setPreserveTextBoxes(false)
              .setExtractTextFromShapes(false);

        assertFalse(config.isNormalizeWhitespace());
        assertFalse(config.isConvertBulletsToMarkdown());
        assertFalse(config.isPreserveTextBoxes());
        assertFalse(config.isExtractTextFromShapes());
    }

    @Test
    void testSlideSpecificOptions() {
        PptConversionConfig config = new PptConversionConfig();

        // Test slide-related options
        assertTrue(config.isIncludeSlideNumbers());
        assertFalse(config.isIncludeHiddenSlides());
        assertFalse(config.isGenerateTableOfContents());

        config.setIncludeSlideNumbers(false)
              .setIncludeHiddenSlides(true)
              .setGenerateTableOfContents(true);

        assertFalse(config.isIncludeSlideNumbers());
        assertTrue(config.isIncludeHiddenSlides());
        assertTrue(config.isGenerateTableOfContents());
    }

    @Test
    void testChartExtractionConfiguration() {
        PptConversionConfig config = new PptConversionConfig();

        // Test chart extraction (default false)
        assertFalse(config.isExtractCharts());

        config.setExtractCharts(true);
        assertTrue(config.isExtractCharts());

        // Test high fidelity config includes charts
        PptConversionConfig highFidelity = PptConversionConfig.createHighFidelityConfig();
        assertTrue(highFidelity.isExtractCharts());

        // Test performance config excludes charts
        PptConversionConfig performance = PptConversionConfig.createPerformanceConfig();
        assertFalse(performance.isExtractCharts());
    }

    @Test
    void testStreamProcessingConfiguration() {
        PptConversionConfig config = new PptConversionConfig();

        // Test stream processing (default false)
        assertFalse(config.isStreamProcessing());

        config.setStreamProcessing(true);
        assertTrue(config.isStreamProcessing());

        // Test performance config enables stream processing
        PptConversionConfig performance = PptConversionConfig.createPerformanceConfig();
        assertTrue(performance.isStreamProcessing());
    }

    @Test
    void testErrorReportingConfiguration() {
        PptConversionConfig config = new PptConversionConfig();

        // Test error reporting defaults
        assertTrue(config.isSkipCorruptedSlides());
        assertTrue(config.isLogDetailedErrors());
        assertFalse(config.isGenerateErrorReport());

        config.setSkipCorruptedSlides(false)
              .setLogDetailedErrors(false)
              .setGenerateErrorReport(true);

        assertFalse(config.isSkipCorruptedSlides());
        assertFalse(config.isLogDetailedErrors());
        assertTrue(config.isGenerateErrorReport());

        // Test strict config
        PptConversionConfig strict = PptConversionConfig.createStrictConfig();
        assertFalse(strict.isSkipCorruptedSlides());
        assertTrue(strict.isLogDetailedErrors());
        assertTrue(strict.isGenerateErrorReport());
    }

    @Test
    void testImageFormatValidation() {
        PptConversionConfig config = new PptConversionConfig();

        // Test valid formats
        config.setImageFormat("png");
        assertEquals("png", config.getImageFormat());

        config.setImageFormat("jpg");
        assertEquals("jpg", config.getImageFormat());

        config.setImageFormat("gif");
        assertEquals("gif", config.getImageFormat());

        // Test case insensitive
        config.setImageFormat("PNG");
        assertEquals("png", config.getImageFormat());

        // Test that any format is accepted (converted to lowercase)
        config.setImageFormat("invalid");
        assertEquals("invalid", config.getImageFormat()); // Accepts any format

        config.setImageFormat(null);
        assertEquals("png", config.getImageFormat()); // Should fallback to default
    }

    @Test
    void testImageSizeConstraints() {
        PptConversionConfig config = new PptConversionConfig();

        // Test that large values are accepted (no maximum constraint)
        config.setMaxImageWidth(5000);
        assertEquals(5000, config.getMaxImageWidth()); // No maximum constraint

        config.setMaxImageHeight(5000);
        assertEquals(5000, config.getMaxImageHeight()); // No maximum constraint

        // Test minimum constraints
        config.setMaxImageWidth(50);
        assertEquals(100, config.getMaxImageWidth()); // Should be clamped to minimum

        config.setMaxImageHeight(50);
        assertEquals(100, config.getMaxImageHeight()); // Should be clamped to minimum
    }

    @Test
    void testImageDirectoryValidation() {
        PptConversionConfig config = new PptConversionConfig();

        // Test valid directory names
        config.setImageOutputDirectory("custom_images");
        assertEquals("custom_images", config.getImageOutputDirectory());

        config.setImageOutputDirectory("img/subfolder");
        assertEquals("img/subfolder", config.getImageOutputDirectory());

        // Test empty string (accepted as-is)
        config.setImageOutputDirectory("");
        assertEquals("", config.getImageOutputDirectory()); // Empty string is accepted

        config.setImageOutputDirectory("   ");
        assertEquals("   ", config.getImageOutputDirectory()); // Whitespace is accepted

        // Test null directory fallback
        config.setImageOutputDirectory(null);
        assertEquals("images", config.getImageOutputDirectory()); // Only null falls back to default
    }

    @Test
    void testMultipleFileExtensionSupport() {
        // Test supported PowerPoint file extensions (only ppt, pptx, pptm)
        assertTrue(converter.supportsExtension("ppt"));
        assertTrue(converter.supportsExtension("pptx"));
        assertTrue(converter.supportsExtension("pptm")); // Macro-enabled

        // Test with dots
        assertTrue(converter.supportsExtension(".ppt"));
        assertTrue(converter.supportsExtension(".pptx"));
        assertTrue(converter.supportsExtension(".pptm"));

        // Test case insensitive
        assertTrue(converter.supportsExtension("PPT"));
        assertTrue(converter.supportsExtension("PPTX"));
        assertTrue(converter.supportsExtension(".PPT"));

        // Test unsupported PowerPoint extensions
        assertFalse(converter.supportsExtension("potx")); // Template - not supported
        assertFalse(converter.supportsExtension("ppsx")); // Slide show - not supported

        // Test unsupported extensions
        assertFalse(converter.supportsExtension("doc"));
        assertFalse(converter.supportsExtension("docx"));
        assertFalse(converter.supportsExtension("pdf"));
        assertFalse(converter.supportsExtension("xls"));
        assertFalse(converter.supportsExtension("xlsx"));
        assertFalse(converter.supportsExtension("txt"));
        assertFalse(converter.supportsExtension("html"));
        assertFalse(converter.supportsExtension(""));
        assertFalse(converter.supportsExtension(null));
    }

    @Test
    void testCorruptedFileHandling() throws IOException {
        // Create a file with PPT extension but invalid content
        Path corruptedFile = tempDir.resolve("corrupted.pptx");
        Files.write(corruptedFile, "This is not a valid PowerPoint file content".getBytes());

        // Test loose mode - should not throw but return error message
        PptConversionConfig looseConfig = new PptConversionConfig().setStrictMode(false);
        assertDoesNotThrow(() -> {
            String result = PptToMarkdownConverter.convertToMarkdown(corruptedFile.toFile(), looseConfig);
            assertNotNull(result);
            assertTrue(result.contains("Error") || result.contains("Failed") || result.contains("conversion"));
        });

        // Test strict mode - should throw exception
        PptConversionConfig strictConfig = new PptConversionConfig().setStrictMode(true);
        assertThrows(RuntimeException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(corruptedFile.toFile(), strictConfig);
        });
    }

    @Test
    void testLargeFileHandling() throws IOException {
        // Create a larger fake PPTX file to test memory handling
        Path largeFile = tempDir.resolve("large.pptx");
        byte[] largeContent = new byte[1024 * 1024]; // 1MB of data
        // Fill with ZIP-like header to pass initial validation
        System.arraycopy(createMinimalPptxBytes(), 0, largeContent, 0, createMinimalPptxBytes().length);
        Files.write(largeFile, largeContent);

        // Test with performance config
        PptConversionConfig performanceConfig = PptConversionConfig.createPerformanceConfig();
        assertDoesNotThrow(() -> {
            String result = PptToMarkdownConverter.convertToMarkdown(largeFile.toFile(), performanceConfig);
            assertNotNull(result);
        });
    }

    @Test
    void testConcurrentConversion() throws InterruptedException {
        // Test thread safety by running multiple conversions concurrently
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    Path testFile = tempDir.resolve("concurrent_test_" + index + ".pptx");
                    Files.write(testFile, createMinimalPptxBytes());

                    PptConversionConfig config = new PptConversionConfig().setStrictMode(false);
                    String result = PptToMarkdownConverter.convertToMarkdown(testFile.toFile(), config);
                    results[index] = result != null;
                } catch (Exception e) {
                    results[index] = false;
                }
            });
        }

        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join(5000); // 5 second timeout per thread
        }

        // Check that all conversions completed (even if they failed gracefully)
        for (boolean result : results) {
            assertTrue(result, "Concurrent conversion should complete without hanging");
        }
    }

    @Test
    void testMemoryLeakPrevention() throws IOException {
        // Test multiple conversions to ensure no memory leaks
        PptConversionConfig config = new PptConversionConfig()
            .setEnableCaching(false) // Disable caching to test cleanup
            .setStrictMode(false);

        for (int i = 0; i < 10; i++) {
            Path testFile = tempDir.resolve("memory_test_" + i + ".pptx");
            Files.write(testFile, createMinimalPptxBytes());

            assertDoesNotThrow(() -> {
                String result = PptToMarkdownConverter.convertToMarkdown(testFile.toFile(), config);
                assertNotNull(result);
            });
        }

        // Force garbage collection to test for memory leaks
        System.gc();
        Thread.yield();
    }

    @Test
    void testConfigurationChaining() {
        // Test that configuration methods can be chained
        PptConversionConfig config = new PptConversionConfig()
            .setIncludeMetadata(true)
            .setIncludeSpeakerNotes(true)
            .setExtractImages(false)
            .setExtractTables(true)
            .setExtractCharts(true)
            .setStrictMode(false)
            .setImageFormat("jpg")
            .setMaxImageWidth(1024)
            .setMaxImageHeight(768)
            .setImageOutputDirectory("custom")
            .setEnableCaching(true)
            .setMaxCacheSize(50)
            .setProcessingTimeout(600);

        // Verify all settings were applied
        assertTrue(config.isIncludeMetadata());
        assertTrue(config.isIncludeSpeakerNotes());
        assertFalse(config.isExtractImages());
        assertTrue(config.isExtractTables());
        assertTrue(config.isExtractCharts());
        assertFalse(config.isStrictMode());
        assertEquals("jpg", config.getImageFormat());
        assertEquals(1024, config.getMaxImageWidth());
        assertEquals(768, config.getMaxImageHeight());
        assertEquals("custom", config.getImageOutputDirectory());
        assertTrue(config.isEnableCaching());
        assertEquals(50, config.getMaxCacheSize());
        assertEquals(600, config.getProcessingTimeout());
    }

    @Test
    void testConfigurationEqualsAndHashCode() {
        PptConversionConfig config1 = new PptConversionConfig()
            .setIncludeMetadata(true)
            .setExtractImages(false)
            .setStrictMode(true);

        PptConversionConfig config2 = new PptConversionConfig()
            .setIncludeMetadata(true)
            .setExtractImages(false)
            .setStrictMode(true);

        // Test equals (if implemented)
        if (config1.getClass().getDeclaredMethods().length > 20) { // Heuristic for equals implementation
            assertEquals(config1.toString().length(), config2.toString().length());
        }

        // Test toString contains key information
        String configString = config1.toString();
        assertTrue(configString.contains("includeMetadata") || configString.contains("true") || configString.contains("false"));
    }

    @Test
    void testPluginStateTransitions() throws Exception {
        // Test complete plugin lifecycle
        assertEquals("STOPPED", converter.getState().toString());

        // Start -> Running
        converter.start();
        assertEquals("RUNNING", converter.getState().toString());

        // Running -> Stopped
        converter.stop();
        assertEquals("STOPPED", converter.getState().toString());

        // Restart after stop
        converter.start();
        assertEquals("RUNNING", converter.getState().toString());

        // Running -> Destroyed
        converter.destroy();
        assertEquals("DESTROYED", converter.getState().toString());

        // Note: The current implementation doesn't prevent restart after destroy
        // This is acceptable behavior for this converter
        assertDoesNotThrow(() -> {
            converter.start();
        });
    }

    @Test
    void testPluginMetadata() {
        var metadata = converter.getMetadata();

        assertNotNull(metadata);
        assertEquals("ppt-to-markdown-converter", metadata.getId());
        assertNotNull(metadata.getName());
        assertNotNull(metadata.getVersion());
        assertNotNull(metadata.getDescription());

        // Test metadata immutability (if implemented)
        String originalId = metadata.getId();
        // metadata.setId("modified"); // Should not be possible or should not affect original
        assertEquals(originalId, metadata.getId());
    }

    @Test
    void testConversionResultValidation() throws IOException {
        Path testFile = tempDir.resolve("result_test.pptx");
        Files.write(testFile, createMinimalPptxBytes());

        try {
            ConversionResult result = converter.convert(testFile.toFile());

            // Test result structure
            assertNotNull(result);
            assertNotNull(result.getStatus());
            assertNotNull(result.getContent());

            // Test status is valid
            assertTrue(result.getStatus() == ConversionResult.Status.SUCCESS ||
                      result.getStatus() == ConversionResult.Status.FAILED ||
                      result.getStatus() == ConversionResult.Status.PARTIAL_SUCCESS);

            // Test content is not empty (even for failed conversions)
            assertNotNull(result.getContent());

            // Test success flag consistency
            boolean expectedSuccess = result.getStatus() == ConversionResult.Status.SUCCESS ||
                                    result.getStatus() == ConversionResult.Status.PARTIAL_SUCCESS;
            assertEquals(expectedSuccess, result.isSuccess());

        } catch (Exception e) {
            // If conversion fails, ensure error message is meaningful
            assertTrue(e.getMessage().contains("PowerPoint") || e.getMessage().contains("convert"));
        }
    }

    @Test
    void testErrorMessageQuality() throws IOException {
        // Test that error messages are informative

        // Null file
        Exception nullException = assertThrows(IllegalArgumentException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(null);
        });
        assertTrue(nullException.getMessage().contains("null") ||
                  nullException.getMessage().contains("file") ||
                  nullException.getMessage().contains("exist"));

        // Non-existent file
        File nonExistent = new File("definitely_does_not_exist.pptx");
        Exception notFoundException = assertThrows(IllegalArgumentException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(nonExistent);
        });
        assertTrue(notFoundException.getMessage().contains("exist") ||
                  notFoundException.getMessage().contains("found") ||
                  notFoundException.getMessage().contains("does not exist"));

        // Wrong extension
        Path wrongExt = tempDir.resolve("wrong.txt");
        Files.write(wrongExt, "content".getBytes());
        Exception extensionException = assertThrows(IllegalArgumentException.class, () -> {
            PptToMarkdownConverter.convertToMarkdown(wrongExt.toFile());
        });
        assertTrue(extensionException.getMessage().contains("extension") ||
                  extensionException.getMessage().contains("format") ||
                  extensionException.getMessage().contains("valid") ||
                  extensionException.getMessage().contains("PowerPoint"));
    }

    @Test
    void testPerformanceConfiguration() {
        PptConversionConfig performanceConfig = PptConversionConfig.createPerformanceConfig();

        // Performance config should optimize for speed
        assertFalse(performanceConfig.isIncludeMetadata());
        assertFalse(performanceConfig.isIncludeSpeakerNotes());
        assertFalse(performanceConfig.isExtractImages());
        assertFalse(performanceConfig.isExtractCharts());
        assertTrue(performanceConfig.isExtractTables()); // Tables are lightweight
        assertTrue(performanceConfig.isEnableCaching());
        assertTrue(performanceConfig.isStreamProcessing());
        assertFalse(performanceConfig.isPreserveFormatting());
    }

    @Test
    void testHighFidelityConfiguration() {
        PptConversionConfig highFidelityConfig = PptConversionConfig.createHighFidelityConfig();

        // High fidelity config should extract everything
        assertTrue(highFidelityConfig.isIncludeMetadata());
        assertTrue(highFidelityConfig.isIncludeSpeakerNotes());
        assertTrue(highFidelityConfig.isExtractImages());
        assertTrue(highFidelityConfig.isExtractTables());
        assertTrue(highFidelityConfig.isExtractCharts());
        assertTrue(highFidelityConfig.isPreserveFormatting());
        assertTrue(highFidelityConfig.isExtractTextFromShapes());
        assertFalse(highFidelityConfig.isStrictMode()); // Should be forgiving for max content
    }

    @Test
    void testStrictConfiguration() {
        PptConversionConfig strictConfig = PptConversionConfig.createStrictConfig();

        // Strict config should have strict error handling
        assertTrue(strictConfig.isStrictMode());
        assertFalse(strictConfig.isSkipCorruptedSlides());
        assertTrue(strictConfig.isLogDetailedErrors());
        assertTrue(strictConfig.isGenerateErrorReport());
    }

    @Test
    void testBoundaryValues() {
        PptConversionConfig config = new PptConversionConfig();

        // Test extreme timeout values (no maximum constraint)
        config.setProcessingTimeout(Integer.MAX_VALUE);
        assertEquals(Integer.MAX_VALUE, config.getProcessingTimeout()); // No maximum constraint

        config.setProcessingTimeout(0);
        assertTrue(config.getProcessingTimeout() >= 30); // Should be clamped to minimum

        // Test extreme cache sizes (no maximum constraint)
        config.setMaxCacheSize(Integer.MAX_VALUE);
        assertEquals(Integer.MAX_VALUE, config.getMaxCacheSize()); // No maximum constraint

        config.setMaxCacheSize(0);
        assertTrue(config.getMaxCacheSize() >= 10); // Should be clamped to minimum

        // Test extreme image dimensions (no maximum constraint)
        config.setMaxImageWidth(Integer.MAX_VALUE);
        assertEquals(Integer.MAX_VALUE, config.getMaxImageWidth()); // No maximum constraint

        config.setMaxImageHeight(Integer.MAX_VALUE);
        assertEquals(Integer.MAX_VALUE, config.getMaxImageHeight()); // No maximum constraint
    }

    @Test
    void testConfigurationValidation() {
        PptConversionConfig config = new PptConversionConfig();

        // Test that invalid configurations are handled gracefully
        assertDoesNotThrow(() -> {
            config.setImageFormat("invalid_format");
            config.setImageOutputDirectory("");
            config.setMaxImageWidth(-100);
            config.setMaxImageHeight(-100);
            config.setMaxCacheSize(-50);
            config.setProcessingTimeout(-300);
        });

        // Verify actual behavior
        assertEquals("invalid_format", config.getImageFormat()); // Accepts any format
        assertEquals("", config.getImageOutputDirectory()); // Accepts empty string
        assertTrue(config.getMaxImageWidth() >= 100); // Should be corrected to minimum
        assertTrue(config.getMaxImageHeight() >= 100); // Should be corrected to minimum
        assertTrue(config.getMaxCacheSize() >= 10); // Should be corrected to minimum
        assertTrue(config.getProcessingTimeout() >= 30); // Should be corrected to minimum
    }

    @Test
    void testFilePathHandling() throws IOException {
        // Test files with special characters in names
        Path specialFile = tempDir.resolve("测试文件 (1) [copy].pptx");
        Files.write(specialFile, createMinimalPptxBytes());

        assertDoesNotThrow(() -> {
            String result = PptToMarkdownConverter.convertToMarkdown(specialFile.toFile());
            assertNotNull(result);
        });

        // Test files with very long names
        String longName = "a".repeat(200) + ".pptx";
        Path longNameFile = tempDir.resolve(longName);
        Files.write(longNameFile, createMinimalPptxBytes());

        assertDoesNotThrow(() -> {
            String result = PptToMarkdownConverter.convertToMarkdown(longNameFile.toFile());
            assertNotNull(result);
        });
    }

    @Test
    void testResourceCleanup() throws IOException {
        // Test that resources are properly cleaned up after conversion
        Path testFile = tempDir.resolve("cleanup_test.pptx");
        Files.write(testFile, createMinimalPptxBytes());

        // Perform multiple conversions to test resource cleanup
        for (int i = 0; i < 5; i++) {
            assertDoesNotThrow(() -> {
                String result = PptToMarkdownConverter.convertToMarkdown(testFile.toFile());
                assertNotNull(result);
            });
        }

        // Test that the file is not locked after conversion
        assertTrue(Files.isReadable(testFile));
        assertTrue(Files.isWritable(testFile));

        // Test that we can delete the file (not locked)
        assertDoesNotThrow(() -> Files.delete(testFile));
    }

    @Test
    void testStaticMethodsConsistency() throws IOException {
        Path testFile = tempDir.resolve("static_test.pptx");
        Files.write(testFile, createEnhancedPptxBytes());

        PptConversionConfig config = new PptConversionConfig().setStrictMode(false);

        // Test that static methods and instance methods produce consistent results
        String staticResult = PptToMarkdownConverter.convertToMarkdown(testFile.toFile(), config);

        try {
            ConversionResult instanceResult = converter.convert(testFile.toFile());

            assertNotNull(staticResult);
            assertNotNull(instanceResult);
            assertNotNull(instanceResult.getContent());

            // Both should handle the invalid file gracefully
            assertTrue(staticResult.length() > 0);
            assertTrue(instanceResult.getContent().length() > 0);
        } catch (Exception e) {
            // If instance method throws exception, static method should also handle gracefully
            assertNotNull(staticResult);
            assertTrue(staticResult.length() > 0);
        }
    }

    @Test
    void testEdgeCaseExtensions() {
        // Test edge cases for extension checking
        assertTrue(converter.supportsExtension("ppt"));
        assertTrue(converter.supportsExtension(".ppt"));
        assertTrue(converter.supportsExtension("PPT"));
        assertTrue(converter.supportsExtension(".PPT"));
        assertTrue(converter.supportsExtension("pPt"));
        assertTrue(converter.supportsExtension(".pPt"));

        // Test with extra dots
        assertFalse(converter.supportsExtension("..ppt"));
        assertFalse(converter.supportsExtension("ppt."));
        assertFalse(converter.supportsExtension(".ppt."));

        // Test with spaces
        assertFalse(converter.supportsExtension(" ppt"));
        assertFalse(converter.supportsExtension("ppt "));
        assertFalse(converter.supportsExtension(" ppt "));

        // Test empty and null
        assertFalse(converter.supportsExtension(""));
        assertFalse(converter.supportsExtension("   "));
        assertFalse(converter.supportsExtension(null));
    }

    @Test
    void testConfigurationImmutability() {
        // Test that preset configurations are not affected by modifications
        PptConversionConfig highFidelity1 = PptConversionConfig.createHighFidelityConfig();
        PptConversionConfig highFidelity2 = PptConversionConfig.createHighFidelityConfig();

        // Modify one configuration
        highFidelity1.setIncludeMetadata(false);

        // The other should remain unchanged
        assertTrue(highFidelity2.isIncludeMetadata());

        // Test that creating new instances gives consistent results
        PptConversionConfig performance1 = PptConversionConfig.createPerformanceConfig();
        PptConversionConfig performance2 = PptConversionConfig.createPerformanceConfig();

        assertEquals(performance1.isIncludeMetadata(), performance2.isIncludeMetadata());
        assertEquals(performance1.isExtractImages(), performance2.isExtractImages());
        assertEquals(performance1.isStreamProcessing(), performance2.isStreamProcessing());
    }

    /**
     * Enhanced helper method to create more realistic PPTX bytes
     */
    private byte[] createEnhancedPptxBytes() {
        // Create a more complete ZIP header that might pass more validation
        byte[] zipHeader = {
            0x50, 0x4B, 0x03, 0x04, // ZIP signature
            0x14, 0x00, 0x00, 0x00, // Version, flags
            0x08, 0x00, 0x00, 0x00, // Compression method, time
            0x00, 0x00, 0x00, 0x00, // CRC32
            0x00, 0x00, 0x00, 0x00, // Compressed size
            0x00, 0x00, 0x00, 0x00, // Uncompressed size
            0x08, 0x00, 0x00, 0x00, // Filename length, extra length
        };

        byte[] filename = "[Content_Types].xml".getBytes();
        byte[] result = new byte[zipHeader.length + filename.length];
        System.arraycopy(zipHeader, 0, result, 0, zipHeader.length);
        System.arraycopy(filename, 0, result, zipHeader.length, filename.length);

        return result;
    }
}
