package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.util.rtf.RtfConversionConfig;
import com.talkweb.ai.indexer.util.rtf.RtfConversionMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Advanced test class for improved RTF to Markdown converter
 * Tests RTF compatibility, structure conversion, and information extraction
 *
 * <AUTHOR> Assistant
 * @version 2.0
 */
class RtfToMarkdownConverterAdvancedTest {

    private RtfToMarkdownConverter converter;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        PluginMetadata metadata = PluginMetadata.builder()
                .id("rtf-converter-advanced-test")
                .name("RTF Converter Advanced Test")
                .version("2.0.0")
                .description("Advanced test RTF converter with enhanced features")
                .className("com.talkweb.ai.indexer.util.RtfToMarkdownConverter")
                .build();
        
        converter = new RtfToMarkdownConverter(metadata);
        
        // Configure for advanced parsing with all features enabled
        RtfConversionConfig config = RtfConversionConfig.builder()
                .mode(RtfConversionMode.LOOSE)
                .useAdvancedParsing(true)
                .preserveFormatting(true)
                .convertTables(true)
                .includeMetadata(true)
                .build();
        converter.setConfig(config);
    }

    @Test
    void testRtfDocumentCompatibility() throws Exception {
        File rtfFile = createRtfWithVersionAndEncoding();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("RTF compatibility test result:\n" + content);
        
        // Should handle different RTF versions and encodings
        assertFalse(content.trim().isEmpty());
    }

    @Test
    void testTableStructureConversion() throws Exception {
        File rtfFile = createRtfWithTable();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("Table conversion test result:\n" + content);
        
        // Should contain table structure
        assertTrue(content.contains("|") || content.contains("table"));
    }

    @Test
    void testListStructureConversion() throws Exception {
        File rtfFile = createRtfWithLists();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("List conversion test result:\n" + content);
        
        // Should contain list markers
        assertTrue(content.contains("-") || content.contains("1.") || content.contains("list"));
    }

    @Test
    void testHyperlinkConversion() throws Exception {
        File rtfFile = createRtfWithHyperlinks();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("Hyperlink conversion test result:\n" + content);
        
        // Should contain link format
        assertTrue(content.contains("[") && content.contains("]") && content.contains("(") && content.contains(")") 
                   || content.contains("link") || content.contains("http"));
    }

    @Test
    void testAdvancedFormattingConversion() throws Exception {
        File rtfFile = createRtfWithAdvancedFormatting();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("Advanced formatting test result:\n" + content);
        
        // Should contain various formatting
        assertTrue(content.contains("**") || content.contains("*") || content.contains("~~") 
                   || content.contains("<sup>") || content.contains("<sub>"));
    }

    @Test
    void testUnicodeSupport() throws Exception {
        File rtfFile = createRtfWithUnicode();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getContent());
        
        String content = result.getContent();
        System.out.println("Unicode support test result:\n" + content);
        
        // Should handle Unicode characters
        assertFalse(content.trim().isEmpty());
    }

    @Test
    void testMetadataExtraction() throws Exception {
        File rtfFile = createRtfWithMetadata();
        
        ConversionResult result = converter.convert(rtfFile);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // Check if metadata was extracted (simplified check)
        // Note: Metadata extraction would be available through context or result properties
        System.out.println("Metadata extraction test completed");
    }

    /**
     * Creates RTF file with version and encoding information
     */
    private File createRtfWithVersionAndEncoding() throws IOException {
        File rtfFile = tempDir.resolve("version_encoding.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0\\deflang1033" +
                "{\\fonttbl{\\f0\\froman\\fcharset0 Times New Roman;}{\\f1\\fswiss\\fcharset0 Arial;}}" +
                "{\\colortbl;\\red255\\green0\\blue0;\\red0\\green255\\blue0;\\red0\\green0\\blue255;}" +
                "{\\info{\\title Document Title}{\\author Test Author}{\\creatim\\yr2023\\mo12\\dy15}}" +
                "\\f0\\fs24 This is a test document with RTF version 1 and ANSI encoding. " +
                "\\f1 This text uses Arial font. " +
                "\\cf1 This text is red. \\cf0 " +
                "\\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates RTF file with table structure
     */
    private File createRtfWithTable() throws IOException {
        File rtfFile = tempDir.resolve("table.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0" +
                "{\\fonttbl{\\f0 Times New Roman;}}" +
                "\\f0\\fs24 " +
                "Table Example:\\par" +
                "\\trowd\\cellx2000\\cellx4000\\cellx6000" +
                "\\intbl Header 1\\cell Header 2\\cell Header 3\\cell\\row" +
                "\\trowd\\cellx2000\\cellx4000\\cellx6000" +
                "\\intbl Cell 1\\cell Cell 2\\cell Cell 3\\cell\\row" +
                "\\trowd\\cellx2000\\cellx4000\\cellx6000" +
                "\\intbl Data 1\\cell Data 2\\cell Data 3\\cell\\row" +
                "\\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates RTF file with list structures
     */
    private File createRtfWithLists() throws IOException {
        File rtfFile = tempDir.resolve("lists.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0" +
                "{\\fonttbl{\\f0 Times New Roman;}}" +
                "\\f0\\fs24 " +
                "Unordered List:\\par" +
                "{\\pn\\pnlvl1\\pnf0\\pnindent360\\pnstart1\\pndec{\\pntxta.}}" +
                "\\fi-360\\li360 First item\\par" +
                "\\fi-360\\li360 Second item\\par" +
                "\\fi-360\\li360 Third item\\par" +
                "\\par" +
                "Ordered List:\\par" +
                "{\\pn\\pnlvl1\\pnf0\\pnindent360\\pnstart1\\pnord{\\pntxta.}}" +
                "\\fi-360\\li360 First numbered item\\par" +
                "\\fi-360\\li360 Second numbered item\\par" +
                "\\fi-360\\li360 Third numbered item\\par" +
                "\\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates RTF file with hyperlinks
     */
    private File createRtfWithHyperlinks() throws IOException {
        File rtfFile = tempDir.resolve("hyperlinks.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0" +
                "{\\fonttbl{\\f0 Times New Roman;}}" +
                "\\f0\\fs24 " +
                "This document contains hyperlinks:\\par" +
                "Visit {\\field{\\*\\fldinst{HYPERLINK \"https://www.example.com\"}}{\\fldrslt{\\ul\\cf1 Example Website}}}\\par" +
                "Email us at {\\field{\\*\\fldinst{HYPERLINK \"mailto:<EMAIL>\"}}{\\fldrslt{\\ul\\cf1 <EMAIL>}}}\\par" +
                "\\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates RTF file with advanced formatting
     */
    private File createRtfWithAdvancedFormatting() throws IOException {
        File rtfFile = tempDir.resolve("advanced_formatting.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0" +
                "{\\fonttbl{\\f0 Times New Roman;}}" +
                "\\f0\\fs24 " +
                "Advanced Formatting Examples:\\par" +
                "\\b Bold text \\b0 and \\i italic text \\i0\\par" +
                "\\ul Underlined text \\ul0 and \\strike strikethrough text \\strike0\\par" +
                "Normal text with \\super superscript \\super0 and \\sub subscript \\sub0\\par" +
                "\\fs36 Large text \\fs24 and \\fs12 small text \\fs24\\par" +
                "\\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates RTF file with Unicode characters
     */
    private File createRtfWithUnicode() throws IOException {
        File rtfFile = tempDir.resolve("unicode.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0" +
                "{\\fonttbl{\\f0 Times New Roman;}}" +
                "\\f0\\fs24 " +
                "Unicode Examples:\\par" +
                "Copyright symbol: \\u169?\\par" +
                "Euro symbol: \\u8364?\\par" +
                "Chinese characters: \\u20013?\\u22269?\\par" +
                "Mathematical symbols: \\u8721? \\u8734? \\u8730?\\par" +
                "\\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }

    /**
     * Creates RTF file with metadata
     */
    private File createRtfWithMetadata() throws IOException {
        File rtfFile = tempDir.resolve("metadata.rtf").toFile();
        
        String rtfContent = "{\\rtf1\\ansi\\deff0" +
                "{\\fonttbl{\\f0 Times New Roman;}}" +
                "{\\info" +
                "{\\title Test Document Title}" +
                "{\\author Test Author Name}" +
                "{\\subject Document Subject}" +
                "{\\keywords RTF, test, metadata}" +
                "{\\creatim\\yr2023\\mo12\\dy15\\hr10\\min30}" +
                "{\\revtim\\yr2023\\mo12\\dy16\\hr14\\min45}" +
                "}" +
                "\\f0\\fs24 " +
                "This document contains metadata information.\\par" +
                "Check the document properties for details.\\par" +
                "\\par}";
        
        try (FileWriter writer = new FileWriter(rtfFile)) {
            writer.write(rtfContent);
        }
        
        return rtfFile;
    }
}
