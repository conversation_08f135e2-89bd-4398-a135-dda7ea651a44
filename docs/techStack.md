# 技术栈

本文档记录了本项目的关键技术选型，详细的技术上下文请参考 [memory-bank/techContext.md](../memory-bank/techContext.md)。

## 项目状态概览 (2025-06-23)
- **完成度**: 95%
- **测试通过率**: 99.7% (357/358)
- **支持格式**: 8种文档格式
- **发布状态**: 基本就绪

## 核心框架与语言
- **核心语言**: Java 21
- **构建工具**: Maven 3.9+
- **框架**: Spring Boot 3.5.3
- **架构模式**: 插件化架构 (Java SPI), 责任链模式

## AI 与机器学习 ✅ 已集成
- **AI 集成**: Spring AI 1.0
- **文档摘要**: DocumentSummaryService
- **向量嵌入**: DocumentEmbeddingService
- **智能处理**: AiEnhancedDocumentProcessor

## 命令行工具
- **CLI 解析**: Picocli 4.7.5
- **命令结构**: DocConverterCommand, ConvertCommand, PluginCommand

## 文档处理库 ✅ 全面增强
- **Microsoft Office**: Apache POI (增强版转换器)
  - Word (DOCX): 多版本兼容、样式保留
  - Excel (XLSX): 合并单元格、公式处理、多工作表
  - PowerPoint (PPTX): 幻灯片内容、演讲者备注
- **PDF**: Apache PDFBox (页面分割、加密支持、结构保留)
- **HTML**: Jsoup (框架支持: Bootstrap, Tailwind, jQuery, Ant Design)
- **RTF**: RTF Parser Kit (格式保留、编码支持)
- **ODT**: OpenDocument 支持 (XML解析、元数据提取)
- **图片 OCR**: Tesseract OCR (可选集成)
- **Markdown 处理**: CommonMark

## 异步与并发 ✅ 已优化
- **异步处理**: 虚拟线程 (Java 21) + CompletableFuture
- **并发服务**: ConcurrentProcessingService
- **缓存管理**: CacheManager (TTL + 容量限制)

## 测试与质量 ✅ 高覆盖率
- **测试框架**: JUnit 5, Mockito, AssertJ
- **测试统计**: 358个测试，99.7%通过率 (357/358)
- **失败测试**: 仅1个非关键热加载时序测试
- **代码质量**: Checkstyle, SpotBugs
- **代码规模**: 137个Java文件，30,214行代码
- **测试覆盖**: 核心功能100%覆盖，AI服务完整测试

## 监控与服务
- **文件监控**: FileWatcherService (实时文件变更检测)
- **插件管理**: PluginWatcher (热加载支持)
- **性能监控**: Micrometer, Prometheus, Grafana (计划)

## 增强功能特性
- **表格处理**: 智能框架识别、合并单元格处理
- **结构保留**: 文档层次结构、元数据提取
- **错误处理**: 完善的异常处理和恢复机制
- **配置管理**: 灵活的转换配置和模式选择
- **插件系统**: 热加载、生命周期管理、配置验证
- **性能优化**: 虚拟线程、缓存管理、并发处理

## 支持的文档格式 ✅ 8种格式
1. **TXT** - 纯文本文档
2. **HTML** - 网页文档 (支持主流CSS框架)
3. **PDF** - 便携式文档格式 (支持页面分割、加密)
4. **DOCX** - Microsoft Word文档
5. **XLSX** - Microsoft Excel电子表格
6. **PPTX** - Microsoft PowerPoint演示文稿
7. **RTF** - 富文本格式
8. **ODT** - OpenDocument文本格式

## 发布就绪状态
- ✅ 核心功能完成
- ✅ 高质量测试覆盖
- ✅ AI功能集成
- ✅ 性能优化
- 🚧 用户文档完善中
- ⏳ 可选OCR功能
