# New Converter Architecture Guide

## Overview

This document describes the new converter architecture implemented as part of the refactoring project. The new architecture provides better separation of concerns, improved performance, and enhanced maintainability.

## Architecture Components

### 1. Core Interfaces

#### BaseConverter<T, R>
The fundamental interface for all converters.

```java
public interface BaseConverter<T, R> {
    R convert(T input) throws ConversionException;
    boolean supports(T input);
}
```

#### DocumentConverter
Specialized interface for document conversion operations.

```java
public interface DocumentConverter extends BaseConverter<File, ConversionResult> {
    ConversionResult convert(File input, ConversionContext context) throws ConversionException;
    Set<String> getSupportedExtensions();
    ConversionCapabilities getCapabilities();
    ConversionMetadata getMetadata();
}
```

### 2. Abstract Base Classes

#### AbstractDocumentConverter
Provides common functionality for all document converters:

- **Caching**: Automatic result caching with configurable policies
- **Validation**: Input validation and error handling
- **Performance**: Dynamic cache sizing and memory optimization
- **Template Method**: Consistent conversion flow

```java
public abstract class AbstractDocumentConverter implements DocumentConverter {
    @Override
    public final ConversionResult convert(File input, ConversionContext context) throws ConversionException {
        // 1. Validate input
        validateInput(input, context);
        
        // 2. Check cache
        String cacheKey = getCacheKey(input, context);
        ConversionResult cachedResult = cache.get(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }
        
        // 3. Perform conversion
        ConversionResult result = doConvert(input, context);
        
        // 4. Cache result
        if (result != null && result.isSuccess()) {
            cache.put(cacheKey, result);
        }
        
        return result;
    }
    
    protected abstract ConversionResult doConvert(File input, ConversionContext context) throws ConversionException;
}
```

### 3. Adapter Pattern

#### ConverterPluginAdapter
Bridges the gap between the new converter architecture and the existing plugin system:

```java
public class ConverterPluginAdapter implements Plugin {
    private final DocumentConverter converter;
    private final PluginMetadata metadata;
    
    @Override
    public ProcessingResult process(File inputFile, ProcessingContext context) throws DocumentProcessingException {
        // Convert new context to old context
        ConversionContext conversionContext = adaptContext(context);
        
        // Perform conversion
        ConversionResult result = converter.convert(inputFile, conversionContext);
        
        // Convert result back to old format
        return adaptResult(result);
    }
}
```

### 4. Configuration and Context

#### ConversionContext
Provides flexible configuration options for conversions:

```java
public class ConversionContext {
    private final ConversionOptions options;
    private final String mode;
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        public Builder option(String key, Object value) { ... }
        public Builder mode(String mode) { ... }
        public ConversionContext build() { ... }
    }
}
```

#### ConversionCapabilities
Describes what a converter can do:

```java
public class ConversionCapabilities {
    public enum Features {
        HEADINGS, TABLES, LISTS, IMAGES, METADATA
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public boolean hasFeature(Features feature) { ... }
    public <T> T getCapability(String name, Class<T> type, T defaultValue) { ... }
}
```

## Performance Optimizations

### 1. Dynamic Caching

The new architecture includes intelligent caching that adapts to system conditions:

- **Memory-aware sizing**: Cache sizes adjust based on available memory
- **LRU eviction**: Least recently used items are evicted first
- **TTL support**: Automatic expiration of cached items

### 2. Streaming Processing

For large files, the architecture supports streaming processing:

```java
StreamingProcessor processor = new StreamingProcessor(bufferSize, useDirectBuffers);
processor.processFileStreaming(inputFile, chunkProcessor, outputFile);
```

### 3. Concurrent Processing

Enhanced concurrent processing with virtual threads:

```java
ConcurrentProcessingService service = new ConcurrentProcessingService();
List<ConversionResult> results = service.processParallel(files, converter::convert);
```

## Migration Guide

### Converting Old Converters

1. **Extend AbstractDocumentConverter** instead of implementing Plugin directly
2. **Implement doConvert()** method with your conversion logic
3. **Define capabilities** in getCapabilities() method
4. **Update configuration** to use ConverterPluginAdapterFactory

#### Before (Old Architecture):
```java
public class MyConverter implements Plugin, DocumentConverter {
    public ProcessingResult process(File inputFile, ProcessingContext context) {
        // Conversion logic mixed with plugin lifecycle
    }
}
```

#### After (New Architecture):
```java
public class MyConverter extends AbstractDocumentConverter {
    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) {
        // Pure conversion logic
    }
    
    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.HEADINGS)
                .feature(ConversionCapabilities.Features.TABLES)
                .build();
    }
}
```

### Configuration Updates

Update your Spring configuration to use the adapter factory:

```java
@Bean
public Plugin myConverterPlugin() {
    MyConverter converter = new MyConverter();
    PluginMetadata metadata = PluginMetadata.builder()
            .id("my-converter")
            .name("My Document Converter")
            .version("3.0.0")
            .description("Converts documents with new architecture")
            .className(MyConverter.class.getName())
            .build();
    
    return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
}
```

## Best Practices

### 1. Converter Implementation

- **Keep doConvert() pure**: Only conversion logic, no caching or validation
- **Use ConversionContext**: Access configuration through the context object
- **Handle errors gracefully**: Throw ConversionException with meaningful messages
- **Support capabilities**: Accurately describe what your converter can do

### 2. Performance Considerations

- **Use streaming for large files**: Check file size and use StreamingProcessor when appropriate
- **Configure cache appropriately**: Set reasonable cache sizes and TTL values
- **Monitor memory usage**: Use PerformanceOptimizer for memory-aware processing

### 3. Testing

- **Test conversion logic**: Focus on the doConvert() method
- **Test with various contexts**: Verify behavior with different configuration options
- **Test error conditions**: Ensure proper exception handling
- **Test performance**: Verify caching and streaming work correctly

## Examples

See the `examples/` directory for complete working examples of:

- Basic converter implementation
- Advanced converter with streaming support
- Performance optimization techniques
- Testing strategies

## Troubleshooting

### Common Issues

1. **Compilation errors**: Ensure all imports are updated to new packages
2. **Cache not working**: Verify cache configuration and key generation
3. **Performance issues**: Check if streaming should be used for large files
4. **Plugin not loading**: Verify adapter configuration in Spring context

### Debug Tips

- Enable debug logging for `com.talkweb.ai.indexer.core` package
- Use PerformanceOptimizer metrics to monitor system health
- Check cache statistics with ConversionCacheManager.getAllCacheStatistics()

## Future Enhancements

The new architecture is designed to support future enhancements:

- **Plugin hot-reloading**: Dynamic loading/unloading of converters
- **Distributed processing**: Support for distributed conversion workloads
- **Advanced caching**: Redis or other external cache backends
- **Metrics integration**: Prometheus/Micrometer metrics support
