# Converter API Reference

## Core Interfaces

### DocumentConverter

The main interface for document conversion operations.

#### Methods

##### convert(File input, ConversionContext context)

Converts a document file to Markdown format.

**Parameters:**
- `input` (File): The input document file
- `context` (ConversionContext): Configuration and options for the conversion

**Returns:**
- `ConversionResult`: The result of the conversion operation

**Throws:**
- `ConversionException`: If the conversion fails

**Example:**
```java
DocumentConverter converter = new PdfToMarkdownConverter();
ConversionContext context = ConversionContext.builder()
    .option("extractImages", true)
    .option("preserveFormatting", true)
    .build();

ConversionResult result = converter.convert(inputFile, context);
if (result.isSuccess()) {
    String markdown = result.getContent();
    // Process the markdown content
}
```

##### getSupportedExtensions()

Returns the file extensions supported by this converter.

**Returns:**
- `Set<String>`: Set of supported file extensions (without dots)

**Example:**
```java
Set<String> extensions = converter.getSupportedExtensions();
// Returns: ["pdf", "PDF"]
```

##### getCapabilities()

Returns the capabilities of this converter.

**Returns:**
- `ConversionCapabilities`: Description of converter capabilities

**Example:**
```java
ConversionCapabilities caps = converter.getCapabilities();
boolean supportsImages = caps.hasFeature(ConversionCapabilities.Features.IMAGES);
```

##### getMetadata()

Returns metadata about this converter.

**Returns:**
- `ConversionMetadata`: Converter metadata including name, version, description

## Configuration Classes

### ConversionContext

Provides configuration options for conversion operations.

#### Builder Pattern

```java
ConversionContext context = ConversionContext.builder()
    .option("key", value)
    .mode("STRICT")
    .build();
```

#### Common Options

| Option | Type | Description | Default |
|--------|------|-------------|---------|
| `conversionMode` | String | Conversion mode (STRICT/LOOSE) | "LOOSE" |
| `extractImages` | Boolean | Extract images from document | false |
| `preserveFormatting` | Boolean | Preserve original formatting | true |
| `includeMetadata` | Boolean | Include document metadata | false |
| `maxFileSize` | Integer | Maximum file size in bytes | 100MB |
| `outputEncoding` | String | Output text encoding | "UTF-8" |

### ConversionOptions

Manages conversion options with type safety.

#### Methods

##### getStringOption(String key, String defaultValue)

Gets a string option value.

**Example:**
```java
String mode = options.getStringOption("conversionMode", "LOOSE");
```

##### getBooleanOption(String key, boolean defaultValue)

Gets a boolean option value.

**Example:**
```java
boolean extractImages = options.getBooleanOption("extractImages", false);
```

##### getIntOption(String key, int defaultValue)

Gets an integer option value.

**Example:**
```java
int maxSize = options.getIntOption("maxFileSize", 100 * 1024 * 1024);
```

## Result Classes

### ConversionResult

Represents the result of a conversion operation.

#### Properties

- `status` (Status): SUCCESS, FAILURE, or PARTIAL
- `inputPath` (String): Path to the input file
- `outputPath` (String): Path to the output file (if applicable)
- `content` (String): The converted content
- `message` (String): Status message or error description

#### Methods

##### isSuccess()

Checks if the conversion was successful.

**Returns:**
- `boolean`: true if conversion succeeded

##### getContent()

Gets the converted content.

**Returns:**
- `String`: The converted Markdown content

##### getMessage()

Gets the status message.

**Returns:**
- `String`: Status message or error description

## Capability Classes

### ConversionCapabilities

Describes what a converter can do.

#### Features Enum

```java
public enum Features {
    HEADINGS,    // Supports heading conversion
    TABLES,      // Supports table conversion
    LISTS,       // Supports list conversion
    IMAGES,      // Supports image extraction
    METADATA     // Supports metadata extraction
}
```

#### Methods

##### hasFeature(Features feature)

Checks if the converter supports a specific feature.

**Example:**
```java
boolean supportsTables = capabilities.hasFeature(ConversionCapabilities.Features.TABLES);
```

##### getCapability(String name, Class<T> type, T defaultValue)

Gets a specific capability value.

**Example:**
```java
Integer maxFileSize = capabilities.getCapability("maxFileSize", Integer.class, 100 * 1024 * 1024);
```

## Performance Classes

### PerformanceOptimizer

Provides performance optimization for conversions.

#### Methods

##### optimizeForLargeFile(long fileSizeBytes)

Gets optimization settings for large files.

**Returns:**
- `ConversionOptimizationSettings`: Recommended settings

**Example:**
```java
PerformanceOptimizer optimizer = new PerformanceOptimizer();
ConversionOptimizationSettings settings = optimizer.optimizeForLargeFile(fileSize);

if (settings.isUseStreamProcessing()) {
    // Use streaming processor
}
```

### StreamingProcessor

Provides memory-efficient processing for large files.

#### Methods

##### processFileStreaming(File inputFile, Function<byte[], String> chunkProcessor, File outputFile)

Processes a file in streaming fashion.

**Example:**
```java
StreamingProcessor processor = new StreamingProcessor();
processor.processFileStreaming(inputFile, chunk -> {
    // Process each chunk
    return processChunk(chunk);
}, outputFile);
```

## Exception Handling

### ConversionException

Main exception type for conversion errors.

#### Common Scenarios

```java
try {
    ConversionResult result = converter.convert(file, context);
} catch (ConversionException e) {
    switch (e.getErrorCode()) {
        case FILE_NOT_FOUND:
            // Handle missing file
            break;
        case UNSUPPORTED_FORMAT:
            // Handle unsupported format
            break;
        case CONVERSION_FAILED:
            // Handle conversion failure
            break;
    }
}
```

## Caching

### ConversionCacheManager

Manages caching for conversion results.

#### Methods

##### getCache(String name, CacheConfig config)

Gets or creates a cache instance.

**Example:**
```java
CacheConfig config = CacheConfig.builder()
    .type(CacheType.LRU)
    .maxSize(1000)
    .expireAfterWrite(30, TimeUnit.MINUTES)
    .build();

Cache<String, ConversionResult> cache = ConversionCacheManager.getCache("myCache", config);
```

##### clearAllCaches()

Clears all caches.

##### getAllCacheStatistics()

Gets statistics for all caches.

**Returns:**
- `CacheStatistics`: Combined statistics

## Usage Examples

### Basic Conversion

```java
// Create converter
PdfToMarkdownConverter converter = new PdfToMarkdownConverter();

// Create context
ConversionContext context = ConversionContext.builder()
    .option("extractImages", false)
    .build();

// Convert file
ConversionResult result = converter.convert(pdfFile, context);

if (result.isSuccess()) {
    String markdown = result.getContent();
    Files.writeString(Paths.get("output.md"), markdown);
}
```

### Advanced Configuration

```java
ConversionContext context = ConversionContext.builder()
    .option("conversionMode", "STRICT")
    .option("extractImages", true)
    .option("imageOutputDirectory", "images/")
    .option("preserveFormatting", true)
    .option("includeMetadata", true)
    .option("maxFileSize", 50 * 1024 * 1024) // 50MB
    .build();
```

### Performance Optimization

```java
PerformanceConfigurationService perfService = new PerformanceConfigurationService();

if (perfService.shouldUseStreamingProcessing(inputFile)) {
    StreamingProcessor processor = perfService.createOptimizedStreamingProcessor(inputFile);
    // Use streaming processor
} else {
    // Use regular processing
}
```

### Error Handling

```java
try {
    ConversionResult result = converter.convert(file, context);
    
    if (result.getStatus() == ConversionResult.Status.PARTIAL) {
        logger.warn("Partial conversion: {}", result.getMessage());
    }
    
} catch (ConversionException e) {
    logger.error("Conversion failed: {}", e.getMessage(), e);
    
    // Check if it's a recoverable error
    if (e.isRecoverable()) {
        // Retry with different settings
    }
}
```
